/**
 * Azure B2C Service for user management operations
 * Handles B2C user creation, authentication, and related operations
 */

const { logError, logInfo } = require('../common/logging')
const graphService = require('./graph-service')
const emailService = require('./email-service')
const { generateSecurePassword } = require('../utils/password-utils')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })
const crypto = require('crypto')

class B2CService {
  /**
   * Create a user in Azure B2C
   * @param {Object} user - User object with B2C properties
   * @returns {Object} - Created B2C user data
   */
  async createB2CUser(user) {
    try {
      var Token = cache.get(`graphToken`)
      if (!Token || Token.expiresOn.getTime() < new Date().getTime()) {
        Token = await graphService.getToken()
        cache.set('graphToken', {
          accessToken: Token.accessToken,
          expiresOn: Token.expiresOn,
        })
      }

      // Check if the user already exists in AAD
      const existingUser = await graphService.getUserByPrincipalName(
        Token.accessToken,
        user.identities[0].issuerAssignedId,
      )
      if (existingUser) {
        console.log(
          `User with email ${user.identities[0].issuerAssignedId} already exists in AAD`,
        )
        throw new Error(
          `User with email ${user.identities[0].issuerAssignedId} already exists in AAD`,
        )
      }

      // Create the user if it doesn't exist
      var data = await graphService.createUser(Token.accessToken, user)
      return data
    } catch (error) {
      // Handle GraphError specifically - preserve it for upstream handling
      if (error.name === 'GraphError') {
        logError(`GraphError creating B2C user:`, error)
        throw error // Re-throw GraphError as-is to preserve all properties
      }

      if (error.code === 'USER_ALREADY_EXISTS') {
        throw error
      }

      logError(`Error creating B2C user:`, error)
      throw error
    }
  }

  /**
   * Generate PKCE challenge for OAuth flow
   * @returns {Object} - Object containing codeVerifier and codeChallenge
   */
  generatePKCEChallenge() {
    // Generate code verifier (43-128 characters)
    const codeVerifier = crypto.randomBytes(32).toString('base64url')

    // Generate code challenge (SHA256 hash of verifier, base64url encoded)
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url')

    return { codeVerifier, codeChallenge }
  }

  /**
   * Send welcome email with B2C setup instructions
   * @param {string} email - User email address
   * @param {string} name - User display name
   * @param {string} temporaryPassword - Temporary password for first login
   * @param {boolean} isAdmin - Whether this is for admin (organization creation) or regular user
   */
  async sendWelcomeEmailWithB2CSetup(
    email,
    name,
    temporaryPassword,
    isAdmin = false,
  ) {
    try {
      const { codeChallenge } = this.generatePKCEChallenge()
      const nonce = `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`
      const clientRequestId = `${Date.now()}-${crypto
        .randomBytes(8)
        .toString('hex')}`

      const state = {
        id: `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`,
        meta: { interactionType: 'popup' },
      }

      // Determine redirect URI based on user type
      const redirectUri = isAdmin
        ? process.env.BASE_ADMIN_URL
        : process.env.BASE_URL

      const b2cLoginUrl =
        `https://${process.env.TENANT_NAME}.b2clogin.com/${process.env.TENANT_NAME}.onmicrosoft.com/${process.env.signin_policy}/oauth2/v2.0/authorize?` +
        `client_id=${process.env.CLIENT_ID}&` +
        `scope=${encodeURIComponent(
          `${process.env.CLIENT_ID} openid profile offline_access`,
        )}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `client-request-id=${clientRequestId}&` +
        `response_mode=fragment&` +
        `response_type=code&` +
        `x-client-SKU=msal.js.browser&` +
        `x-client-VER=3.18.0&` +
        `client_info=1&` +
        `code_challenge=${codeChallenge}&` +
        `code_challenge_method=S256&` +
        `nonce=${nonce}&` +
        `state=${encodeURIComponent(JSON.stringify(state))}&` +
        `login_hint=${encodeURIComponent(email)}`

      await emailService.sendWelcomeEmailWithB2CCredentials(
        email,
        name,
        temporaryPassword,
        b2cLoginUrl,
        isAdmin,
      )

      logInfo(
        `Welcome email with proper OAuth PKCE URL sent to: ${email} (${
          isAdmin ? 'Admin' : 'User'
        })`,
      )
      logInfo(`B2C OAuth URL (${isAdmin ? 'Admin' : 'User'}): ${b2cLoginUrl}`)
    } catch (error) {
      logError(`Failed to send welcome email to: ${email}`, error)
      throw error
    }
  }

  /**
   * Delete B2C user by ID
   * @param {string} userId - B2C user ID
   */
  async deleteB2CUser(userId) {
    try {
      await graphService.deleteUser(userId)
      logInfo(`B2C user deleted successfully: ${userId}`)
    } catch (error) {
      logError(`Failed to delete B2C user: ${userId}`, error)
      throw error
    }
  }

  /**
   * Update user email in Azure B2C by creating new user and deleting old one
   * @param {string} oldB2CUserId - Current B2C user ID
   * @param {string} newEmail - New email address
   * @param {string} userName - User display name
   * @returns {Object} - New B2C user data
   */
  async updateUserEmail(oldB2CUserId, newEmail, userName) {
    try {
      var Token = cache.get(`graphToken`)
      if (!Token || Token.expiresOn.getTime() < new Date().getTime()) {
        Token = await graphService.getToken()
        cache.set('graphToken', {
          accessToken: Token.accessToken,
          expiresOn: Token.expiresOn,
        })
      }

      // Check if the new email already exists in AAD
      const existingUser = await graphService.getUserByPrincipalName(
        Token.accessToken,
        newEmail,
      )
      if (existingUser) {
        logError(`User with email ${newEmail} already exists in AAD`)
        throw new Error(`User with email ${newEmail} already exists in AAD`)
      }

      // Generate temporary password for the new user
      const temporaryPassword = generateSecurePassword()

      // Create new B2C user with new email
      const newB2CUser = {
        accountEnabled: true,
        displayName: userName,
        identities: [
          {
            signInType: 'emailAddress',
            issuer: `${process.env.TENANT_NAME}.onmicrosoft.com`,
            issuerAssignedId: newEmail,
          },
        ],
        passwordProfile: {
          forceChangePasswordNextSignIn: true,
          password: temporaryPassword,
        },
        passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
      }

      const newUserData = await graphService.createUser(
        Token.accessToken,
        newB2CUser,
      )
      logInfo(`New B2C user created successfully for email: ${newEmail}`)

      // Delete old B2C user
      try {
        await graphService.deleteUser(Token.accessToken, oldB2CUserId)
        logInfo(`Old B2C user deleted successfully: ${oldB2CUserId}`)
      } catch (deleteError) {
        logError(`Failed to delete old B2C user: ${oldB2CUserId}`, deleteError)
        // If deletion fails, we should still continue as the new user is created
      }

      return {
        newB2CUser: newUserData,
        temporaryPassword: temporaryPassword,
      }
    } catch (error) {
      logError(`Error updating user email in B2C:`, error)
      throw error
    }
  }
}

module.exports = new B2CService()
