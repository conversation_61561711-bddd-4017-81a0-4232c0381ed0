const { app } = require('@azure/functions');
const medicalHistoryAddictionHandler = require('../handlers/medical-history-addiction-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('medical-history-addiction', {
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    authLevel: 'function',
    route: 'patient/lifestyle/medical-history-addiction',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        
        const decode = context.extraInputs.get('decode');
        const patientId = request.query.get("patientId");
        const id = request.query.get("id");

        try {
            switch (request.method) {
                case HttpMethod.get:
                    if (!patientId) {
                        return jsonResponse('Missing patientId parameter', HttpStatusCode.BadRequest);
                    }
                    
                    const result = await medicalHistoryAddictionHandler.getMedicalHistoryAddiction(patientId);
                    if (!result) {
                        return jsonResponse('No medical history found for the patient', HttpStatusCode.NotFound);
                    }
                    return jsonResponse(result);

                case HttpMethod.post:
                    if (!patientId) {
                        return jsonResponse('Missing patientId parameter', HttpStatusCode.BadRequest);
                    }
                    
                    const postPayload = await request.json();
                    if (!postPayload) {
                        return jsonResponse('Missing request body', HttpStatusCode.BadRequest);
                    }
                    
                    const createResult = await medicalHistoryAddictionHandler.createMedicalHistoryAddiction(
                        patientId, 
                        postPayload, 
                        decode.oid
                    );
                    return jsonResponse(createResult);

                case HttpMethod.put:
                    if (!id) {
                        return jsonResponse('Missing id parameter', HttpStatusCode.BadRequest);
                    }
                    
                    const putPayload = await request.json();
                    if (!putPayload) {
                        return jsonResponse('Missing request body', HttpStatusCode.BadRequest);
                    }
                    
                    const updateResult = await medicalHistoryAddictionHandler.updateMedicalHistoryAddiction(
                        id, 
                        putPayload, 
                        decode.oid
                    );
                    return jsonResponse(updateResult);

                case HttpMethod.delete:
                    if (!id) {
                        return jsonResponse('Missing id parameter', HttpStatusCode.BadRequest);
                    }
                    
                    const deleteResult = await medicalHistoryAddictionHandler.deleteMedicalHistoryAddiction(id);
                    return jsonResponse(deleteResult);

                default:
                    return jsonResponse('Unsupported HTTP method', HttpStatusCode.MethodNotAllowed);
            }
        } catch (error) {
            context.log('Error in medical-history-addiction function:', error.message);
            
            // Handle specific error types
            if (error.message.includes('already exists')) {
                return jsonResponse({
                    error: {
                        code: "CONFLICT",
                        message: error.message
                    }
                }, HttpStatusCode.Conflict);
            }

            if (error.message.includes('not found')) {
                return jsonResponse({
                    error: {
                        code: "NOT_FOUND", 
                        message: error.message
                    }
                }, HttpStatusCode.NotFound);
            }

            // Generic server error
            return jsonResponse({
                error: {
                    code: "INTERNAL_SERVER_ERROR",
                    message: "An internal server error occurred",
                    details: error.message
                }
            }, HttpStatusCode.InternalServerError);
        }
    }
});