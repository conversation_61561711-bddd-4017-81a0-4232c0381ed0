const { OpenAI } = require('openai');
const { Configuration, OpenAIApi } = require("azure-openai");
const url =  process.env.OPENAI_ENDPOINT;
const key = process.env.OPENAI_KEY;
const openai = new OpenAI({
    baseURL: url,
    apiKey: key
});

const model = process.env.OPENAI_MODEL;

const azureOpenAI = new OpenAIApi(
    new Configuration({
        apiKey: key,
        azure: {
            apiKey: key,
            endpoint: url,
            deploymentName: model
        }
    })
)

const logging = require('../common/logging');


class OpenAIService {

    async chatCompletion(classification, prompt) {
        try {
            var messages = [
                { "role": "system", "content": `${classification}` },
                { "role": "user", "content": `${prompt}` }
            ];
            
            var result = await azureOpenAI.createChatCompletion({
                messages: messages,
                temperature: 0,
                top_p: 0,
                max_tokens: 1200
            })

            var data = result.data.choices[0].message.content;
            return data;
        } catch (error) {
            logging.logError(`Unable to get openai chat completion`, error);
            return null;
        }
    }

}

module.exports = new OpenAIService();