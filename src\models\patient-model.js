const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')
class PatientModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.name = data.name || ''
    this.sex = data.sex || ''
    this.dob = data.dob || ''
    this.height = data.height || ''
    this.weight = data.weight || ''
    this.address = data.address || {}
    this.contact = {
      phone: data.contact?.phone || '',
      email: data.contact?.email || '',
    }
    this.insurance = {
      provider: data.insurance?.provider || '',
      id: data.insurance?.id || '',
      url: data.insurance?.url || '',
    }
    this.id = data.id || ''
    this.organizationId = data.organizationId || ''
    this.last_consultation_date = data.last_consultation_date || null
    this.idProof = data.idProof || ''
    this.maritalStatus = data.maritalStatus || ''
    this.proof = {
      type: data.proof?.type || '',
      url: data.proof?.url || '',
      aadharNumber: data.proof?.aadharNumber || '',
      abhaNumber: data.proof?.abhaNumber || '',
    }
  }
}

module.exports = PatientModel
