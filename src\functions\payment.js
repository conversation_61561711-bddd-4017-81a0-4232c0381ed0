const { app } = require('@azure/functions')
const paymentHandler = require('../handlers/payment-handler')

app.http('create-payment-order', {
  methods: ['POST'],
  route: 'payments/create-order',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Creating payment order')
    return await paymentHandler.createOrder(req)
  }
})

app.http('verify-payment', {
  methods: ['POST'],
  route: 'payments/verify',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Verifying payment')
    return await paymentHandler.verifyPayment(req)
  }
})

app.http('payment-webhook', {
  methods: ['POST'],
  route: 'payments/webhook',
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log('Processing payment webhook')
    return await paymentHandler.handleWebhook(req)
  }
})

app.http('payment-details', {
  methods: ['GET'],
  route: 'payments/details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment details')
    return await paymentHandler.getPaymentDetails(req)
  }
})

app.http('organization-payments', {
  methods: ['GET'],
  route: 'payments/organization',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching organization payments')
    return await paymentHandler.getOrganizationPayments(req)
  }
})

app.http('payment-statistics', {
  methods: ['GET'],
  route: 'payments/stats',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment statistics')
    return await paymentHandler.getPaymentStatistics(req)
  }
})

app.http('search-payments', {
  methods: ['GET'],
  route: 'payments/search',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Searching payments')
    return await paymentHandler.searchPayments(req)
  }
})
