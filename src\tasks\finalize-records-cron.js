const cron = require('node-cron')
const patientService = require('../services/patient-service')

const finalizePatientLifestyleCron = () => {
    cron.schedule('0 * * * *', async () => {
      console.log('[Cron] Patient lifestyle finalization job running...')

      try {
        const cutoffDate = new Date(
          Date.now() - 24 * 60 * 60 * 1000,
        ).toISOString()
        const query = `SELECT * FROM c WHERE c.status != 'finalized' AND c.created_on < '${cutoffDate}'`

        const records = await patientService.getPatientLifeStyleByQuery(query)
        if (!records?.length) {
          console.log('No records to finalize')
          return
        }

        for (const record of records) {
          await patientService.patchPatientLifeStyle(record.id, {
            status: 'finalized',
            updated_on: new Date().toISOString(),
          })
          console.log(`Finalized record ${record.id}`)
        }
      } catch (error) {
        console.error('Cron job failed:', error)
      }
    })
}

module.exports = finalizePatientLifestyleCron