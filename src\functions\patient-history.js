const { app } = require('@azure/functions');
const patientHandler = require("../handlers/patient-handler");
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const { HttpMethod } = require('../common/constant');

app.http('patient-history', {
    methods: ['GET', 'POST', 'PUT'],
    authLevel: 'function',
    route: "patient/history",
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const decode = context.extraInputs.get('decode');

        const patientId = req.query.get('patientId');
        if (!patientId) {
            return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest);
        }

        switch (req.method) {
            case HttpMethod.get:
                var startDate = req.query.get('startDate');
                var endDate = req.query.get('endDate') || new Date().toISOString();

                if (startDate && endDate) {
                    var data = await patientHandler.GetPatientHistoryByDate(patientId, startDate, endDate);
                    return jsonResponse(data);
                } else {
                    var data = await patientHandler.GetPatientHistory(patientId);
                    return jsonResponse(data)
                }
            case HttpMethod.post:
            case HttpMethod.put:
                if (!req.body) {
                    return jsonResponse(`Missing patientHistory payload`, HttpStatusCode.BadRequest);
                }
                var patientHistory = await req.json();
                var data = null;
                if (req.method == HttpMethod.post) {
                    data = await patientHandler.CreatePatientHistory(patientId, patientHistory, decode.oid);
                    if (!data) {
                        return jsonResponse(`Unable to create Patient History`, HttpStatusCode.Forbidden);
                    }
                }
                if (req.method == HttpMethod.put) {
                    data = await patientHandler.updatePatientHistory(patientId, patientHistory, decode.oid);
                    if (!data) {
                        return jsonResponse(`Unable to update Patient History`, HttpStatusCode.Forbidden);
                    }
                }
                return jsonResponse(data);
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
