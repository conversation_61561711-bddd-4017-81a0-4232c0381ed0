/**
 * Product Form Mapping Utility
 * Maps full product form names to their short forms
 */

class ProductFormMapper {
  constructor() {
    this.productFormMapping = {
      Capsule: 'CAP',
      Tablet: 'TAB',
      Ointment: 'OINT',
      Injection: 'INJ',
      Infusion: 'INF',
      Syrup: 'SYP',
      Cream: 'CRM',
      'Chewing Gum': 'GUM',
      Powder: 'POWDER',
      'Eye Drop': 'E/D',
      'Tablet PR': 'TAB PR',
      Gel: 'GEL',
      Suspension: 'SUSP',
      'Vaginal Suppository': 'VAG SUPP',
      'Tablet SR': 'TAB SR',
      'Oral Drop': 'DROP',
      'Oral Suspension': 'ORAL SUSP',
      Pessary: 'PESS',
      'Ophthalmic Solution': 'OPH SOL',
      'Tablet MD': 'TAB MD',
      'Eye Gel': 'E/GEL',
      'Capsule SR': 'CAP SR',
      'Powder for Injection': 'POWDER',
      'Soft Gelatin Capsule': 'GEL CAP',
      'Tablet DR': 'TAB DR',
      Kit: 'KIT',
      'Tablet DT': 'TAB DT',
    }

    // Create reverse mapping for lookup
    this.shortFormMapping = {}
    Object.entries(this.productFormMapping).forEach(([fullForm, shortForm]) => {
      this.shortFormMapping[shortForm] = fullForm
    })
  }

  /**
   * Get short form for a given product form
   * @param {string} productForm - Full product form name
   * @returns {string} Short form or original if not found
   */
  getShortForm(productForm) {
    if (!productForm) return ''

    // Try exact match first
    if (this.productFormMapping[productForm]) {
      return this.productFormMapping[productForm]
    }

    // Try case-insensitive match
    const lowerProductForm = productForm.toLowerCase()
    const matchedKey = Object.keys(this.productFormMapping).find(
      (key) => key.toLowerCase() === lowerProductForm,
    )

    if (matchedKey) {
      return this.productFormMapping[matchedKey]
    }

    // Return original if no mapping found
    return productForm
  }

  /**
   * Get full form for a given short form
   * @param {string} shortForm - Short form
   * @returns {string} Full form or original if not found
   */
  getFullForm(shortForm) {
    if (!shortForm) return ''

    return this.shortFormMapping[shortForm] || shortForm
  }

  /**
   * Get all available product forms
   * @returns {Object} Object with fullForms and shortForms arrays
   */
  getAllForms() {
    return {
      fullForms: Object.keys(this.productFormMapping),
      shortForms: Object.values(this.productFormMapping),
      mapping: this.productFormMapping,
    }
  }

  /**
   * Check if a product form has a mapping
   * @param {string} productForm - Product form to check
   * @returns {boolean} True if mapping exists
   */
  hasMapping(productForm) {
    if (!productForm) return false

    return (
      this.productFormMapping.hasOwnProperty(productForm) ||
      Object.keys(this.productFormMapping).some(
        (key) => key.toLowerCase() === productForm.toLowerCase(),
      )
    )
  }

  /**
   * Transform medicine object to use short forms
   * @param {Object} medicine - Medicine object
   * @returns {Object} Medicine object with short form
   */
  transformMedicineToShortForm(medicine) {
    if (!medicine) return medicine

    const transformed = { ...medicine }

    // Handle different property names for product form
    if (medicine.productForm) {
      transformed.productForm = this.getShortForm(medicine.productForm)
    }

    if (medicine.DrugFormulation) {
      transformed.DrugFormulation = this.getShortForm(medicine.DrugFormulation)
    }

    if (medicine.drugForm) {
      transformed.drugForm = this.getShortForm(medicine.drugForm)
    }

    return transformed
  }

  /**
   * Transform array of medicines to use short forms
   * @param {Array} medicines - Array of medicine objects
   * @returns {Array} Array of medicine objects with short forms
   */
  transformMedicinesToShortForm(medicines) {
    if (!Array.isArray(medicines)) return medicines

    return medicines.map((medicine) =>
      this.transformMedicineToShortForm(medicine),
    )
  }

  /**
   * Transform prescription object to use short forms
   * @param {Object} prescription - Prescription object
   * @returns {Object} Prescription object with short forms
   */
  transformPrescription(prescription) {
    if (!prescription) return prescription

    return {
      ...prescription,
      medicines: this.transformMedicinesToShortForm(prescription.medicines),
    }
  }

  /**
   * Transform array of prescriptions to use short forms
   * @param {Array} prescriptions - Array of prescription objects
   * @returns {Array} Array of prescription objects with short forms
   */
  transformPrescriptions(prescriptions) {
    if (!Array.isArray(prescriptions)) return prescriptions

    return prescriptions.map((prescription) =>
      this.transformPrescription(prescription),
    )
  }
}

module.exports = new ProductFormMapper()
