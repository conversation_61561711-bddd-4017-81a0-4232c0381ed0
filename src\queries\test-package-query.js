const getTestsByPackageId = (packageId) => {
  return `SELECT c.tests FROM c WHERE c.id = "${packageId}"`
}

const getTestPackageById = (packageId) => {
  return `SELECT * FROM c WHERE c.id = "${packageId}"`
}

const getTestPackageByName = (name) => {
  return `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}"`
}

const getTestPackagesByType = (type) => {
  return `SELECT c.id, c.name, c.type FROM c WHERE c.type = "${type.trim()}"`
}

const getUserSpecificPackages = (userId) => {
  return `SELECT * FROM c WHERE c.userId = "${userId}" AND c.type = "user"`
}

module.exports = {
  getTestsByPackageId,
  getTestPackageById,
  getTestPackageByName,
  getTestPackagesByType,
  getUserSpecificPackages,
}
