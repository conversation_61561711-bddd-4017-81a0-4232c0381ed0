const axios = require('axios')
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy-hybrid', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy-hybrid',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const searchType = request.query.get('type') || 'auto'
    
    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      console.log(`Hybrid ICD Search - Query: "${query}", Type: ${searchType}`)
      
      // Determine if the query looks like an ICD code 
      const isIcdCode = /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(query.trim())
      console.log(`Detected as ICD code: ${isIcdCode}`)
      
      let results = []
      
      // Strategy 1: Try NLM API first (better for code lookups)
      try {
        console.log(`Trying NLM API...`)
        const nlmResponse = await axios.get(
          'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
          {
            params: { 
              terms: query,
              df: 'code,title,definition,type,chapter',
              maxList: 20
            },
            timeout: 8000,
          }
        )
        
        if (nlmResponse.data && nlmResponse.data[1] && nlmResponse.data[1].length > 0) {
          console.log(`NLM API: Found ${nlmResponse.data[0]} total results`)
          
          // Convert NLM format to standard format
          results = nlmResponse.data[3].map((item, index) => ({
            id: `nlm-${index}`,
            title: item[1] || 'Unknown',
            theCode: item[0] || '',
            definition: item[2] || '',
            type: item[3] || '',
            chapter: item[4] || '',
            score: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 1.0 : 0.9,
            isLeaf: true,
            source: 'ICD-11 (NLM)',
            api: 'nlm'
          }))
          
          // If we found exact code match, return immediately
          if (isIcdCode) {
            const exactMatch = results.find(r => r.theCode.toUpperCase() === query.toUpperCase())
            if (exactMatch) {
              console.log(`Found exact code match in NLM API`)
              return jsonResponse({
                error: false,
                errorMessage: null,
                resultChopped: false,
                wordSuggestionsChopped: false,
                guessType: 1,
                uniqueSearchId: "",
                words: null,
                destinationEntities: [exactMatch]
              })
            }
          }
        }
      } catch (error) {
        console.log(`NLM API failed: ${error.message}`)
      }
      
      // Strategy 2: If NLM didn't find results or failed, try original API
      if (results.length === 0) {
        try {
          console.log(`Trying original ICD API...`)
          const originalResponse = await axios.get(
            'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
            {
              params: { q: query },
              headers: {
                Accept: 'application/json',
                'Content-Type': 'application/json',
                'API-Version': 'v2',
                'Accept-Language': 'en',
              },
              timeout: 10000,
            }
          )
          
          if (originalResponse.data && originalResponse.data.destinationEntities && originalResponse.data.destinationEntities.length > 0) {
            console.log(`Original API: Found ${originalResponse.data.destinationEntities.length} results`)
            
            // Convert to standard format and add to results
            const originalResults = originalResponse.data.destinationEntities.map(entity => ({
              ...entity,
              title: cleanTitle(entity.title),
              source: 'ICD-11 (Original)',
              api: 'original'
            }))
            
            results = results.concat(originalResults)
          }
        } catch (error) {
          console.log(`Original API failed: ${error.message}`)
        }
      }
      
      // Return results
      if (results.length > 0) {
        return jsonResponse({
          error: false,
          errorMessage: null,
          resultChopped: false,
          wordSuggestionsChopped: false,
          guessType: 2,
          uniqueSearchId: "",
          words: null,
          destinationEntities: results
        })
      }
      
      // No results from either API
      return jsonResponse({
        error: false,
        errorMessage: isIcdCode 
          ? `ICD code "${query}" not found in any database. This code may not exist or may be in a different format.`
          : `No results found for "${query}". Try a different search term.`,
        resultChopped: false,
        wordSuggestionsChopped: false,
        guessType: 0,
        uniqueSearchId: "",
        words: null,
        destinationEntities: []
      })
      
    } catch (error) {
      console.error('Error in hybrid ICD search:', error.message)
      return jsonResponse({
        error: 'Error fetching ICD data',
        details: error.message,
        query: query
      }, 500)
    }
  },
})

// Clean HTML tags from title
function cleanTitle(title) {
  if (!title) return 'Unknown'
  return title.replace(/<[^>]*>/g, '')
}
