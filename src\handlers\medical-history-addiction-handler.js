const medicalHistoryAddictionService = require('../services/medical-history-addiction-service');
const MedicalHistoryAddictionModel = require('../models/medical-history-addiction-model');
const { logError, logInfo } = require('../common/logging');
const { v4: uuidv4 } = require('uuid');

class MedicalHistoryAddictionHandler {

    async getMedicalHistoryAddiction(patientId) {
        try {
            logInfo(`Getting medical history addiction for patient: ${patientId}`);
            const result = await medicalHistoryAddictionService.getMedicalHistoryAddictionByPatientId(patientId);
            return result;
        } catch (error) {
            logError('Error getting medical history addiction:', error);
            return null;
        }
    }

    async createMedicalHistoryAddiction(patientId, payload, createdBy) {
        try {
            logInfo(`Creating medical history addiction for patient: ${patientId}`);
            
            // Check if record already exists
            const existingRecord = await medicalHistoryAddictionService.checkExistingRecord(patientId);
            if (existingRecord) {
                throw new Error('Medical history already exists for this patient');
            }

            const medicalHistoryData = {
                ...payload,
                id: uuidv4(),
                patientId: patientId,
                createdBy: createdBy,
                created_by: createdBy,
                updated_by: createdBy,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            const medicalHistoryModel = new MedicalHistoryAddictionModel(medicalHistoryData);
            const result = await medicalHistoryAddictionService.createMedicalHistoryAddiction(medicalHistoryModel);
            return result;
        } catch (error) {
            logError('Error creating medical history addiction:', error);
            throw error;
        }
    }

    async updateMedicalHistoryAddiction(id, payload, updatedBy) {
        try {
            logInfo(`Updating medical history addiction: ${id}`);
            
            // Check if record exists
            const existingRecord = await medicalHistoryAddictionService.getMedicalHistoryAddictionById(id);
            if (!existingRecord) {
                throw new Error('Medical history record not found');
            }

            const updatedData = {
                ...existingRecord,
                ...payload,
                id: id,
                updated_by: updatedBy,
                updatedAt: new Date().toISOString()
            };

            const medicalHistoryModel = new MedicalHistoryAddictionModel(updatedData);
            const result = await medicalHistoryAddictionService.updateMedicalHistoryAddiction(id, medicalHistoryModel);
            return result;
        } catch (error) {
            logError('Error updating medical history addiction:', error);
            throw error;
        }
    }

    async patchMedicalHistoryAddiction(id, patchPayload, updatedBy) {
        try {
            logInfo(`Patching medical history addiction: ${id}`);
            
            // Check if record exists
            const existingRecord = await medicalHistoryAddictionService.getMedicalHistoryAddictionById(id);
            if (!existingRecord) {
                throw new Error('Medical history record not found');
            }

            const patchData = {
                ...patchPayload,
                updated_by: updatedBy,
                updatedAt: new Date().toISOString()
            };

            const result = await medicalHistoryAddictionService.patchMedicalHistoryAddiction(id, patchData);
            return result;
        } catch (error) {
            logError('Error patching medical history addiction:', error);
            throw error;
        }
    }

    async deleteMedicalHistoryAddiction(id) {
        try {
            logInfo(`Deleting medical history addiction: ${id}`);
            
            // Check if record exists
            const existingRecord = await medicalHistoryAddictionService.getMedicalHistoryAddictionById(id);
            if (!existingRecord) {
                throw new Error('Medical history record not found');
            }

            const result = await medicalHistoryAddictionService.deleteMedicalHistoryAddiction(id);
            return result;
        } catch (error) {
            logError('Error deleting medical history addiction:', error);
            throw error;
        }
    }
}

module.exports = new MedicalHistoryAddictionHandler();