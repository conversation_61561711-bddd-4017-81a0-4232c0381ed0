const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const appointmentManageHandler = require('../handlers/appointment-manage-handler');

app.http('queue', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const decode = context.extraInputs.get('decode');
        const queueId = req.query.get('queueId');
        if (!queueId) {
            return jsonResponse(`Missing queueId`, HttpStatusCode.BadRequest);
        }
        switch (req.method) {
            case HttpMethod.get:
                var data = await appointmentManageHandler.getQueueById(queueId);
                return jsonResponse(data);

            case HttpMethod.patch:
                if (!req.body) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var payload = await req.json()
                var data = await appointmentManageHandler.updateQueue(queueId, payload, decode.oid);
                return jsonResponse(data);
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
