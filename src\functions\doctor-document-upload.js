const { app } = require('@azure/functions')
const multipart = require('parse-multipart')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { BlobServiceClient } = require('@azure/storage-blob')

// app.http('doctor-document-upload', {
//     methods: ['POST'],
//     authLevel: 'function',
//     route: 'doctor/document/upload',
//     handler: async (request, context) => {
//         context.log(`Http function processed request for url "${request.url}"`);
//         const decode = context.extraInputs.get('decode');
//         const data = await request.formData();
//         const file = data.get('file');
//         const fileName = file.name;
//         const filesize = file.size;
//         if (filesize >  209715200) {
//             return jsonResponse(`File size should be less than 200MB`, HttpStatusCode.BadRequest);

//         }
//         const buffer = await file.arrayBuffer();
//         const doc_type = data.get('doc_type');
//         const doctorId = data.get('doctorId');
//         if (!doc_type || !doctorId) {
//             return jsonResponse(`Missing doc_type or doctorId`, HttpStatusCode.BadRequest);
//         }
//         const connectionString = process.env.AzureWebJobsStorage || "";
//         const containerName = 'file-uploads';
//         const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
//         const containerClient = blobServiceClient.getContainerClient(containerName);
//         await containerClient.createIfNotExists();
//         const blobName = `doctor/${doctorId}/${doc_type}/${fileName}`;
//         const blockBlobClient = containerClient.getBlockBlobClient(blobName);
//         await blockBlobClient.uploadData(buffer);
//         const blobUrl = blockBlobClient.url;
//         return jsonResponse({ blobUrl });
//     }
// });
app.http('user-document-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'user/document/upload',
  handler: async (request, context) => {
    context.log(`Http function processed request for url "${request.url}"`)
    const decode = context.extraInputs.get('decode')
    const data = await request.formData()
    const file = data.get('file')
    const fileName = file.name
    const filesize = file.size
    if (filesize > 209715200) {
      return jsonResponse(
        `File size should be less than 200MB`,
        HttpStatusCode.BadRequest,
      )
    }
    const buffer = await file.arrayBuffer()
    const doc_type = data.get('doc_type')
    const userId = data.get('userId')
    if (!doc_type || !userId) {
      return jsonResponse(
        `Missing doc_type or userId`,
        HttpStatusCode.BadRequest,
      )
    }
    const connectionString = process.env.AzureWebJobsStorage || ''
    const containerName = 'file-uploads'
    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString)
    const containerClient = blobServiceClient.getContainerClient(containerName)
    await containerClient.createIfNotExists()
    const blobName = `user/${userId}/${doc_type}/${fileName}`
    const blockBlobClient = containerClient.getBlockBlobClient(blobName)
    await blockBlobClient.uploadData(buffer)
    const blobUrl = blockBlobClient.url
    return jsonResponse({ blobUrl })
  },
})
