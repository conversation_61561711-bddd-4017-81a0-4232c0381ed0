# Payment System - Product Requirements Document (PRD)

## Overview
The Payment System is a comprehensive solution for handling payments in the EMR (Electronic Medical Records) system, supporting multiple payment types including patient registration, consultations, prescriptions, and lab tests through Razorpay integration.

## Architecture

### Layer Structure
```
┌─────────────────────────────────────────────────────────────┐
│                    API Functions Layer                      │
│  (src/functions/payment.js - Azure Functions endpoints)     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Handler Layer                            │
│     (src/handlers/payment-handler.js - Request/Response)    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
│   (src/services/payment-service.js - Business Logic)       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Data Access Layer                            │
│  Repository (CRUD) │         Query (Read Operations)       │
│  payment-repository │         payment-query                │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                           │
│              (Azure Cosmos DB - Payments Container)        │
└─────────────────────────────────────────────────────────────┘
```

## API Endpoints

### 1. Organization Payments (Enhanced)
**Endpoint:** `GET /api/payments/organization`

**Parameters:**
- `organizationId` (optional): Organization ID. If not provided, returns all payments across all organizations
- `pageSize` (optional): Number of items per page (default: 20)
- `continuationToken` (optional): Token for pagination
- `patientId` (optional): Filter by patient ID
- `status` (optional): Filter by payment status
- `paymentType` (optional): Filter by payment type
- `startDate` (optional): Filter by start date
- `endDate` (optional): Filter by end date
- `minAmount` (optional): Filter by minimum amount
- `maxAmount` (optional): Filter by maximum amount

**Response:**
```json
{
  "payments": [
    {
      "id": "payment-uuid",
      "razorpayOrderId": "order_xyz",
      "razorpayPaymentId": "pay_abc",
      "amount": 20000,
      "currency": "INR",
      "status": "completed",
      "paymentType": "patient_registration",
      "patientId": "CG94304351",
      "organizationId": "org-uuid",
      "description": "Registration Fee",
      "createdAt": "2025-08-08T08:10:18.724Z",
      "verifiedAt": "2025-08-08T08:10:42.127Z",
      "metadata": {}
    }
  ],
  "continuationToken": "cursor_2025-08-05T10:19:01.296Z",
  "hasMoreResults": true,
  "pageSize": 20,
  "itemCount": 20
}
```

### 2. Create Payment Order
**Endpoint:** `POST /api/payments/create-order`

### 3. Verify Payment
**Endpoint:** `POST /api/payments/verify`

### 4. Payment Webhook
**Endpoint:** `POST /api/payments/webhook`

### 5. Get Payment Details
**Endpoint:** `GET /api/payments/details`

### 6. Payment Statistics
**Endpoint:** `GET /api/payments/stats`

## Key Features

### 1. Flexible Organization Filtering
- **Organization-specific**: When `organizationId` is provided, returns payments for that organization only
- **Global view**: When `organizationId` is omitted, returns all payments across all organizations
- **Admin capability**: Allows super admins to view all payments system-wide

### 2. Advanced Pagination
- **Cursor-based pagination**: Uses timestamp-based cursors for consistent results
- **Proper continuation**: Each page returns different data based on continuation token
- **Performance optimized**: Handles large datasets efficiently

### 3. Comprehensive Filtering
- **Multi-parameter filtering**: Supports filtering by patient, status, type, date range, amount range
- **Flexible combinations**: All filters can be combined for precise queries
- **Performance optimized**: Filters applied at database level

### 4. Clean Architecture
- **Separation of concerns**: Clear boundaries between layers
- **Repository pattern**: CRUD operations isolated in repository
- **Query pattern**: Read operations optimized in query layer
- **Service layer**: Business logic centralized

## Data Flow

### Organization Payments Request Flow
```
1. Client Request
   ↓
2. Azure Function (organization-payments)
   ↓
3. Payment Handler (getOrganizationPayments)
   ↓
4. Payment Service (getPaymentsByOrganization)
   ↓
5. Payment Query (getPaymentsByOrganization)
   ↓
6. Cosmos DB Query Execution
   ↓
7. Response with Pagination Data
```

### Pagination Flow
```
1. First Request (continuationToken = null)
   ↓
2. Query: SELECT * FROM c WHERE [filters] ORDER BY c.createdAt DESC
   ↓
3. Response: { items: [...], continuationToken: "cursor_2025-08-05T10:19:01.296Z" }
   ↓
4. Next Request (continuationToken = "cursor_2025-08-05T10:19:01.296Z")
   ↓
5. Query: SELECT * FROM c WHERE [filters] AND c.createdAt < '2025-08-05T10:19:01.296Z' ORDER BY c.createdAt DESC
   ↓
6. Response: { items: [...], continuationToken: "cursor_2025-08-04T15:30:22.123Z" }
```

## File Structure

### Core Files
- `src/functions/payment.js` - API endpoint definitions
- `src/handlers/payment-handler.js` - Request/response handling
- `src/services/payment-service.js` - Business logic
- `src/repositories/payment-repository.js` - CRUD operations
- `src/queries/payment-query.js` - Read operations and complex queries

### Key Methods

#### PaymentQuery
- `getPaymentsByOrganization(organizationId, pageSize, continuationToken, filters)`
- `getPaymentById(paymentId)`
- `getPaymentByRazorpayOrderId(razorpayOrderId)`
- `getPaymentStatistics(organizationId, startDate, endDate)`
- `searchPayments(filters)`

#### PaymentRepository
- `createPayment(payment)`
- `updatePayment(payment)`
- `getPaymentById(paymentId)` - delegates to query
- `getPaymentByRazorpayOrderId(razorpayOrderId)` - delegates to query

## Testing Scenarios

### 1. Pagination Testing
```bash
# First page
GET /api/payments/organization?organizationId=123&pageSize=3

# Second page
GET /api/payments/organization?organizationId=123&pageSize=3&continuationToken=cursor_2025-08-05T10:19:01.296Z

# Third page
GET /api/payments/organization?organizationId=123&pageSize=3&continuationToken=cursor_2025-08-04T15:30:22.123Z
```

### 2. Global Payments Testing
```bash
# All payments across organizations
GET /api/payments/organization?pageSize=10

# All payments with filters
GET /api/payments/organization?status=completed&paymentType=consultation&pageSize=10
```

### 3. Filtered Payments Testing
```bash
# Organization with filters
GET /api/payments/organization?organizationId=123&status=completed&startDate=2025-08-01&endDate=2025-08-31

# Patient-specific payments
GET /api/payments/organization?organizationId=123&patientId=CG94304351

# Amount range filtering
GET /api/payments/organization?organizationId=123&minAmount=100&maxAmount=500
```

## Performance Considerations

### 1. Database Optimization
- **Indexed queries**: All queries use indexed fields (organizationId, createdAt, status)
- **Cursor-based pagination**: Avoids OFFSET/LIMIT performance issues
- **Selective filtering**: Filters applied at database level, not in application

### 2. Response Optimization
- **Minimal data transfer**: Only required fields returned
- **Efficient pagination**: Continuation tokens prevent duplicate results
- **Proper caching**: Responses can be cached based on parameters

### 3. Scalability
- **Stateless design**: No server-side session state
- **Horizontal scaling**: Architecture supports multiple instances
- **Database partitioning**: Cosmos DB partitioning by organizationId

## Security

### 1. Authentication
- All endpoints require function-level authentication
- Webhook endpoint uses anonymous access with signature verification

### 2. Authorization
- Organization-specific data access controls
- Admin-level access for global payment views

### 3. Data Protection
- Sensitive payment data encrypted at rest
- PCI compliance through Razorpay integration
- Audit trails for all payment operations

## Monitoring & Logging

### 1. Application Insights
- Request/response logging
- Performance metrics
- Error tracking

### 2. Custom Metrics
- Payment success/failure rates
- API response times
- Pagination performance

### 3. Alerts
- Failed payment notifications
- High error rate alerts
- Performance degradation warnings
