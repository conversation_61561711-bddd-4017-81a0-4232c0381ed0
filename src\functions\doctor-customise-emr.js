const { app } = require('@azure/functions');
const logging = require('../common/logging');
const doctorHandler = require('../handlers/doctor-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('doctor-customise-emr', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'function',
    route: 'doctor/customise-emr',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');
        const doctorId = request.query.get("doctorId");
        switch (request.method) {
            case HttpMethod.get:
                if (!doctorId) {
                    return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
                }
                var res = await doctorHandler.getDoctorCustomiseEmr(doctorId);
                return jsonResponse(res);
            case HttpMethod.post:
                if (!doctorId) {
                    return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
                }
                const payload = await request.json()
                if (!payload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                payload.doctorId = doctorId;
                var data = await doctorHandler.createDoctorCustomiseEmr(payload, decode.oid)
                return jsonResponse(data)
            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await doctorHandler.patchDoctorCustomiseEmr(id, patchPayload, decode.oid)
                return jsonResponse(data);
        
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
