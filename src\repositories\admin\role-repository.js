const cosmosDbContext = require('../../cosmosDbContext/comosdb-context')
const roleContainer = 'roles'

class RoleRepository {
  async createRole(data) {
    return cosmosDbContext.createItem(data, roleContainer)
  }

  async updateRole(data) {
    return cosmosDbContext.upsertItem(data.id, data, roleContainer)
  }

  async deleteRole(roleId) {
    console.log('Deleting role with ID:', roleId)
    return cosmosDbContext.deleteItem(roleId, roleId, roleContainer)
  }

  async getRoleById(roleId) {
    const query = `SELECT * FROM c WHERE c.id = "${roleId}"`
    const result = await cosmosDbContext.queryItems(query, roleContainer)
    return result[0]
  }

  async getRoleByName(name, organizationId) {
    const query = `SELECT * FROM c WHERE c.name = "${name}" AND c.organizationId = "${organizationId}"`
    const result = await cosmosDbContext.queryItems(query, roleContainer)
    return result[0]
  }

  async getAllRoles() {
    const query = 'SELECT * FROM c'
    return cosmosDbContext.queryItems(query, roleContainer)
  }

  async getRolesByOrganization(organizationId, search) {
    const query = `
      SELECT * FROM c
      WHERE c.organizationId = "${organizationId}"
      ${search ? `AND CONTAINS(c.name, "${search}")` : ''}
    `
    return cosmosDbContext.queryItems(query, roleContainer)
  }
}

module.exports = new RoleRepository()
