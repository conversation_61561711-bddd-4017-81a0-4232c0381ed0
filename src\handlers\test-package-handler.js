const testPackageService = require('../services/test-package-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')

class TestPackageHandler {
  async createTestPackage(req) {
    try {
      const body = await req.json()
      const { name, type, userId } = body

      if (!name || !type || !userId) {
        return jsonResponse(
          'Test package name, type, and userId are required',
          HttpStatusCode.BadRequest,
        )
      }

      const newTestPackage = await testPackageService.createTestPackage(
        body,
        userId,
      )
      return jsonResponse(newTestPackage, HttpStatusCode.Created)
    } catch (err) {
      return jsonResponse(
        'Error creating test package',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getUserSpecificPackages(req) {
    try {
      const userId = req.query.get('userId')

      if (!userId) {
        return jsonResponse(
          'Query parameter "userId" is required',
          HttpStatusCode.BadRequest,
        )
      }

      const testPackages = await testPackageService.getUserSpecificPackages(
        userId,
      )
      return jsonResponse(testPackages)
    } catch (err) {
      return jsonResponse(
        'Error fetching user-specific test packages',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getTestPackagesByType(req) {
    try {
      const url = new URL(req.url)
      const type = url.searchParams.get('type')

      if (!type) {
        return jsonResponse(
          'Query parameter "type" is required',
          HttpStatusCode.BadRequest,
        )
      }

      const testPackages = await testPackageService.getTestPackagesByType(type)
      return jsonResponse(testPackages)
    } catch (err) {
      return jsonResponse(
        'Error fetching test packages',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getTestsForPackage(req) {
    try {
      const packageId = req.query.get('packageId')
      if (!packageId) {
        return jsonResponse('Missing package ID', HttpStatusCode.BadRequest)
      }

      const data = await testPackageService.getTestsForPackage(packageId)
      return jsonResponse(data)
    } catch (err) {
      return jsonResponse(
        'Error fetching tests for package',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async addTestToPackage(req) {
    try {
      const body = await req.json()
      const { packageId, tests } = body

      if (!packageId || !tests || !Array.isArray(tests)) {
        return jsonResponse(
          'Missing packageId or tests',
          HttpStatusCode.BadRequest,
        )
      }

      const success = await testPackageService.addTestsToPackage(
        packageId,
        tests,
      )
      if (success) {
        return jsonResponse('Test added to package', HttpStatusCode.Created)
      }

      return jsonResponse(
        'Failed to add test to package',
        HttpStatusCode.InternalServerError,
      )
    } catch (err) {
      return jsonResponse(
        'Error adding test to package',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async removeTestFromPackage(req) {
    try {
      const body = await req.json()
      const { packageId, testId } = body

      if (!packageId || !testId) {
        return jsonResponse(
          'Missing packageId or testId',
          HttpStatusCode.BadRequest,
        )
      }

      const success = await testPackageService.removeTestFromPackage(
        packageId,
        testId,
      )
      if (success) {
        return jsonResponse('Test removed from package', HttpStatusCode.OK)
      }

      return jsonResponse(
        'Failed to remove test from package',
        HttpStatusCode.InternalServerError,
      )
    } catch (err) {
      return jsonResponse(
        'Error removing test from package',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateTestPackage(req) {
    try {
      const body = await req.json()
      const { packageId, updates } = body

      if (!packageId || !updates || typeof updates !== 'object') {
        return jsonResponse(
          'Missing packageId or updates',
          HttpStatusCode.BadRequest,
        )
      }

      const updatedPackage = await testPackageService.updateTestPackage(
        packageId,
        updates,
      )
      return jsonResponse(updatedPackage, HttpStatusCode.OK)
    } catch (err) {
      return jsonResponse(
        'Error updating test package',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new TestPackageHandler()
