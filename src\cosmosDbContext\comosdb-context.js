const { CosmosClient } = require('@azure/cosmos')

// Setup Cosmos DB client
const connectionString = process.env.COSMOS_DB_CONNECTIONSTRING // Cosmos DB Endpoint
const databaseId = process.env.COSMOS_DB_DATABASE || 'ArcaaiEMR' // Database name
const client = new CosmosClient(connectionString)
const cosmos_running_mode = process.env.cosmos_running_mode
if (cosmos_running_mode && cosmos_running_mode == 'emulator') {
  process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0'
}
// Container cache to avoid repeated metadata requests
const containerCache = new Map()
const databaseInitialized = { value: false }

// Metadata request semaphore to limit concurrent container access
let metadataRequestCount = 0
const MAX_METADATA_REQUESTS = 2

const getContainer = async (containerId) => {
  // Return cached container if available
  if (containerCache.has(containerId)) {
    return containerCache.get(containerId)
  }

  // Wait if too many metadata requests are in progress
  while (metadataRequestCount >= MAX_METADATA_REQUESTS) {
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  try {
    metadataRequestCount++

    if (!databaseInitialized.value) {
      await client.databases.createIfNotExists({
        id: databaseId,
      })
      databaseInitialized.value = true
    }

    // Initialize container only once per container
    await client.database(databaseId).containers.createIfNotExists({
      id: containerId,
      partitionKey: '/id',
      throughput: 400,
    })

    const container = client.database(databaseId).container(containerId)

    // Cache the container reference
    containerCache.set(containerId, container)

    console.log(`Container ${containerId} cached successfully`)
    return container
  } catch (error) {
    console.error(`Error getting container ${containerId}:`, error)
    throw error
  } finally {
    metadataRequestCount--
  }
}

const cosmosDbContext = {
  createItem: async (item, containerId, retryCount = 0) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    item.created_on = curDate
    item.updated_on = curDate

    try {
      const { resource } = await container.items.create(item)
      return resource
    } catch (error) {
      // Handle 429 rate limiting with exponential backoff
      if (error.code === 429 && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000 // 1s, 2s, 4s + jitter
        console.log(
          `Rate limited on create, retrying in ${delay}ms (attempt ${
            retryCount + 1
          }/3)`,
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
        return module.exports.createItem(item, containerId, retryCount + 1)
      }
      throw error
    }
  },

  readItem: async (id, partitionKey, containerId) => {
    const container = await getContainer(containerId)
    const { resource } = await container.item(id, partitionKey).read()
    return resource
  },

  queryItems: async (sqlQuery, containerId) => {
    const container = await getContainer(containerId)
    const querySpec = { query: sqlQuery }
    const { resources: items } = await container.items
      .query(querySpec)
      .fetchAll()
    return items
  },

  getAllItems: async (containerId, pageSize = 10, continuationToken = null) => {
    const container = await getContainer(containerId)
    const iterator = continuationToken
      ? container.items.readAll({
          maxItemCount: pageSize,
          continuationToken: continuationToken,
        })
      : container.items.readAll({
          maxItemCount: pageSize,
        })
    const { resources: items, continuationToken: nextToken } =
      await iterator.fetchNext()
    return { items, nextToken }
  },

  // getAllItemQuery: async (containerId, queryString, pageSize = 10, continuationToken = null) => {
  //     const container = await getContainer(containerId);
  //     // const querySpec = continuationToken ? {
  //     //     query: queryString,
  //     //     options: {
  //     //         maxItemCount: pageSize,
  //     //         continuationToken: continuationToken
  //     //     }
  //     // } : {
  //     //     query: queryString,
  //     //     options: {
  //     //         maxItemCount: pageSize
  //     //     }
  //     // };
  //     const iterator = continuationToken ? container.items.query(queryString, {
  //         maxItemCount: pageSize,
  //         continuationToken: continuationToken
  //     }) : container.items.query(queryString, {
  //         maxItemCount: pageSize
  //     });
  //     const { resources: items, headers } = await iterator.fetchNext();
  //     const nextToken = headers['x-ms-continuation'];
  //     return { items, nextToken };
  // },
  getAllItemQuery: async (
    containerId,
    queryString,
    pageSize = 10,
    continuationToken = null,
  ) => {
    const container = await getContainer(containerId)

    const querySpec = {
      query: queryString,
    }

    const requestOptions = {
      maxItemCount: pageSize,
    }

    if (continuationToken) {
      requestOptions.continuationToken = continuationToken
    }

    const iterator = container.items.query(querySpec, requestOptions)
    const {
      resources: items,
      headers,
      continuationToken: responseContinuationToken,
    } = await iterator.fetchNext()

    // Try multiple ways to get the continuation token
    const nextToken =
      responseContinuationToken ||
      headers?.['x-ms-continuation'] ||
      headers?.continuationToken
    console.log('kkkk', nextToken)

    return { items, nextToken, continuationToken: nextToken }
  },
  getAllItemQueryWithPagination: async (
    containerId,
    queryString,
    pageSize = 10,
    continuationToken = null,
  ) => {
    try {
      const container = await getContainer(containerId)

      const querySpec = {
        query: queryString,
      }

      const requestOptions = {
        maxItemCount: pageSize,
      }

      if (continuationToken & (continuationToken != null)) {
        requestOptions.continuationToken = continuationToken
      }

      const queryIterator = container.items.query(querySpec, requestOptions)

      const {
        resources,
        headers,
        hasMoreResults,
        continuationToken: responseContinuationToken,
      } = await queryIterator.fetchNext()

      let nextToken = null
      const iteratorHasMore =
        typeof queryIterator.hasMoreResults === 'function'
          ? queryIterator.hasMoreResults()
          : hasMoreResults

      if (iteratorHasMore || hasMoreResults) {
        nextToken = responseContinuationToken || headers?.['x-ms-continuation']

        if (!nextToken && iteratorHasMore) {
          if (resources && resources.length > 0) {
            const lastItem = resources[resources.length - 1]
            nextToken = `cursor_${lastItem.createdAt || lastItem._ts}`
          }
        }
      }

      return {
        items: resources || [],
        nextToken,
        continuationToken: nextToken,
        hasMoreResults: iteratorHasMore,
      }
    } catch (error) {
      console.error('Error in getAllItemQuery:', error)
      throw error
    }
  },

  updateItem: async (item, containerId, retryCount = 0) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    item.updated_on = curDate

    try {
      const { resource } = await container.items.upsert(item)
      return resource
    } catch (error) {
      // Handle 429 rate limiting with exponential backoff
      if (error.code === 429 && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000 // 1s, 2s, 4s + jitter
        console.log(
          `Rate limited, retrying in ${delay}ms (attempt ${retryCount + 1}/3)`,
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
        return module.exports.updateItem(item, containerId, retryCount + 1)
      }
      throw error
    }
  },

  upsertItem: async (id, data, containerId) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    data.updated_on = curDate
    data.id = id
    const { resource } = await container.items.upsert(data)
    return resource
  },

  // patchItem: async (id, updateData, containerId) => {
  //   const container = await getContainer(containerId)
  //   const { resource: existingItem } = await container.item(id, id).read()
  //   if (!existingItem) {
  //     throw new Error(`Item with id ${id} not found`)
  //   }
  //   var curDate = new Date().toISOString()
  //   updateData.updated_on = curDate
  //   //updateData.id = id;
  //   const patchOperations = []
  //   for (const key in updateData) {
  //     const operation = existingItem.hasOwnProperty(key) ? 'replace' : 'add'
  //     patchOperations.push({
  //       op: operation,
  //       path: `/${key}`,
  //       value: updateData[key],
  //     })
  //   }
  //   const { resource: updatedQueue } = await container
  //     .item(id, id)
  //     .patch(patchOperations)
  //   return updatedQueue
  // },
  patchItem: async (id, updateData, containerId) => {
    const container = await getContainer(containerId)
    const { resource: existingItem } = await container.item(id, id).read()
    if (!existingItem) {
      throw new Error(`Item with id ${id} not found`)
    }

    updateData.updated_on = new Date().toISOString()

    const patchOperations = []
    for (const key in updateData) {
      const operation = existingItem.hasOwnProperty(key) ? 'replace' : 'add'
      patchOperations.push({
        op: operation,
        path: `/${key}`,
        value: updateData[key],
      })
    }

    if (containerId === 'DoctorCustomiseEmrs') {
      const chunkArray = (arr, size) => {
        const result = []
        for (let i = 0; i < arr.length; i += size) {
          result.push(arr.slice(i, i + size))
        }
        return result
      }

      const patchChunks = chunkArray(patchOperations, 10)
      let updatedItem = existingItem

      for (const chunk of patchChunks) {
        const { resource } = await container.item(id, id).patch(chunk)
        updatedItem = resource
      }

      return updatedItem
    } else {
      const { resource: updatedItem } = await container
        .item(id, id)
        .patch(patchOperations)
      return updatedItem
    }
  },
  deleteItem: async (id, partitionKey, containerId) => {
    const container = await getContainer(containerId)
    const { resource } = await container.item(id, partitionKey).delete()
    return resource
  },

  // Environment-aware configuration optimized for your RU/s limits
  getEnvironmentConfig: () => {
    const environment = process.env.NODE_ENV || 'development'
    const isProduction = environment === 'production'

    return {
      // Production: 400-4000 RU/s, Development: 100-1000 RU/s
      maxConcurrency: isProduction ? 10 : 6,
      batchSize: isProduction ? 1000 : 500,
      maxRetries: isProduction ? 5 : 7,
      retryDelayMs: isProduction ? 300 : 500,
      delayBetweenBatches: isProduction ? 25 : 50,
      smallDatasetThreshold: isProduction ? 3000 : 1500,
      largeDatasetThreshold: isProduction ? 15000 : 8000,
      environment: isProduction ? 'PRODUCTION' : 'DEVELOPMENT',
    }
  },
}

module.exports = cosmosDbContext
