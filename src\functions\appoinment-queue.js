const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const appointmentManageHandler = require('../handlers/appointment-manage-handler');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('appoinment-queue', {
    methods: ['GET', 'POST', "PATCH"],
    authLevel: 'function',
    route: 'appointment/queue',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);

        const decode = context.extraInputs.get('decode');
        switch (request.method) {
            case HttpMethod.patch:
                var appointmentId = request.query.get('appointmentId')
                if (!appointmentId) {
                    return jsonResponse(`Missing appointmentId`, HttpStatusCode.BadRequest);
                }
                if (!request.body) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest);
                }
                var payload = await request.json();
                var data = await appointmentManageHandler.getAppointmentDetails(appointmentId);
                for (let i = 0; i < data.queues.length; i++) {
                    const e = data.queues[i];
                    await appointmentManageHandler.upsertQueueOnly(e.queueId, payload, decode.oid);
                }
                var datafinal = await appointmentManageHandler.getAppointmentDetails(appointmentId);
                return jsonResponse(datafinal);
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
