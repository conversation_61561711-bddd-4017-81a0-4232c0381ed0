const axios = require('axios')
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy-simple', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy-simple',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const searchType = request.query.get('type') || 'auto'
    
    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      console.log(`ICD Search - Query: "${query}", Type: ${searchType}`)
      
      // Determine if the query looks like an ICD code 
      // ICD codes: A00, A00.1, PB28, 8B92.2, IB94.Z, CA22.Z, MG26, etc.
      const isIcdCode = /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(query.trim())
      console.log(`Detected as ICD code: ${isIcdCode}`)
      
      let searchResults = null
      
      // Always try the search API first (it handles both codes and disease names)
      const response = await axios.get(
        'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
        {
          params: { q: query },
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'API-Version': 'v2',
            'Accept-Language': 'en',
          },
          timeout: 10000,
        }
      )
      
      console.log(`API Response: Found ${response.data?.destinationEntities?.length || 0} entities`)
      
      if (response.data && response.data.destinationEntities) {
        // If searching by ICD code, try to find exact matches first
        if (isIcdCode || searchType === 'code') {
          const exactMatches = response.data.destinationEntities.filter(entity => 
            entity.theCode && entity.theCode.toUpperCase() === query.toUpperCase()
          )
          
          if (exactMatches.length > 0) {
            console.log(`Found ${exactMatches.length} exact code matches`)
            searchResults = { ...response.data, destinationEntities: exactMatches }
          }
        }
        
        // If no exact matches or searching by disease name, return all results
        if (!searchResults) {
          console.log(`Returning all search results`)
          searchResults = response.data
        }
        
        // Clean up the titles (remove HTML tags)
        if (searchResults.destinationEntities) {
          searchResults.destinationEntities = searchResults.destinationEntities.map(entity => ({
            ...entity,
            title: cleanTitle(entity.title)
          }))
        }
        
        return jsonResponse(searchResults)
      }
      
      return jsonResponse({
        query: query,
        searchType: isIcdCode ? 'code' : 'disease',
        error: 'No results found',
        destinationEntities: []
      })
      
    } catch (error) {
      console.error('Error in ICD search:', error.message)
      if (error.response) {
        console.error('API Error Status:', error.response.status)
        console.error('API Error Data:', error.response.data)
      }
      
      return jsonResponse({
        error: 'Error fetching ICD data',
        details: error.message,
        query: query
      }, 500)
    }
  },
})

// Clean HTML tags from title
function cleanTitle(title) {
  if (!title) return 'Unknown'
  // Remove HTML tags like <em class='found'>fever</em>
  return title.replace(/<[^>]*>/g, '')
}
