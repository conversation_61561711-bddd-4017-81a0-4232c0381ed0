const { app } = require('@azure/functions');
const appointmentManageHandler = require('../handlers/appointment-manage-handler');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('book-consultation', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'book-consultation/future',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const patientId = req.query.get('patientId');
        if (!patientId) {
            return jsonResponse('Missing patientId', HttpStatusCode.BadRequest);
        }
        const now = new Date();
        try {
            const data = await appointmentManageHandler.getFutureAppointmentsByPatientId(patientId, now);
            return jsonResponse(data);
        } catch (err) {
            context.log('Error fetching future appointments', err);
            return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
        }
    }
});

app.http('book-consultation-type', {
    methods: ['GET'],
    authLevel: 'function',
    route: 'book-consultation/type',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const patientId = req.query.get('patientId');
        const doctorId = req.query.get('doctorId');
        if (!patientId || !doctorId) {
            return jsonResponse('Missing patientId or doctorId', HttpStatusCode.BadRequest);
        }
        try {
            const visitType = await appointmentManageHandler.getPatientDoctorVisitType(patientId, doctorId);
            return jsonResponse({ visitType });
        } catch (err) {
            context.log('Error checking patient visit type', err);
            return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
        }
    }
});

app.http('update-queue-by-id', {
    methods: ['PATCH'],
    authLevel: 'function',
    route: 'book-consultation/queue',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const queueId = req.query.get('queueId');
        const decode = context.extraInputs.get('decode')

        const queue = await req.json();
        if (!queueId || !queue) {
            return jsonResponse('Missing queueId or queue data', HttpStatusCode.BadRequest);
        }
        try {
            const data = await appointmentManageHandler.updateQueueById(queueId, queue, decode.oid);
            return jsonResponse(data);
        } catch (err) {
            context.log('Error updating queue', err);
            return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
        }
    }
});
