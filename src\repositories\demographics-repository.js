const cosmosDbContext = require('../cosmosDbContext/comosdb-context');
const logging = require('../common/logging');

const patientDemographicsContainer = 'PatientDemographics';

class DemographicsRepository {
    async create(demographics) {
        try {
            const result = await cosmosDbContext.createItem(
                demographics,
                patientDemographicsContainer
            );
            return result;
        } catch (error) {
            logging.logError(
                `Unable to create demographics for patient ${demographics.patientId}`,
                error
            );
            throw error;
        }
    }

    async getByPatientId(patientId) {
        try {
            const query = `SELECT * FROM c WHERE c.patientId = '${patientId}'`;
            const result = await cosmosDbContext.queryItems(
                query,
                patientDemographicsContainer
            );
            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            logging.logError(
                `Unable to get demographics for patient ${patientId}`,
                error
            );
            throw error;
        }
    }

    async getById(demographicsId) {
        try {
            const result = await cosmosDbContext.readItem(
                demographicsId,
                demographicsId,
                patientDemographicsContainer
            );
            return result;
        } catch (error) {
            logging.logError(
                `Unable to get demographics by id ${demographicsId}`,
                error
            );
            throw error;
        }
    }

    async update(demographics) {
        try {
            const result = await cosmosDbContext.updateItem(
                demographics,
                patientDemographicsContainer
            );
            return result;
        } catch (error) {
            logging.logError(
                `Unable to update demographics for patient ${demographics.patientId}`,
                error
            );
            throw error;
        }
    }

    async delete(demographicsId) {
        try {
            const result = await cosmosDbContext.deleteItem(
                demographicsId,
                patientDemographicsContainer
            );
            return result;
        } catch (error) {
            logging.logError(
                `Unable to delete demographics with id ${demographicsId}`,
                error
            );
            throw error;
        }
    }

    async getByQuery(query) {
        try {
            const result = await cosmosDbContext.queryItems(
                query,
                patientDemographicsContainer
            );
            return result;
        } catch (error) {
            logging.logError(
                `Unable to get demographics by query: ${query}`,
                error
            );
            throw error;
        }
    }
}

module.exports = new DemographicsRepository();