const { LabTestStatus } = require('../common/constant')
const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

class LabTestModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)

    this.id = data.id || ''
    this.patientId = data.patientId || ''
    this.status = data.status || LabTestStatus.NOT_PAID
    this.labTests = (data.labTests || []).map((test) => this.mapLabTest(test))
  }

  mapLabTest(test) {
    return {
      testName: test.testName || '',
      testId: test.testId || '',
      qty: test.qty || 1,
      instructions: test.instructions || '',
      cost: test.cost !== undefined ? test.cost : null,
      toBeDoneBy: test.toBeDoneBy || null,
      date: test.date || null,
      results: test.results || null,
      reference: test.reference || null,
      status: test.status || LabTestStatus.UPLOAD,
    }
  }
}

module.exports = LabTestModel
