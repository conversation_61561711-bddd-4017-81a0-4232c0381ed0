const { app } = require('@azure/functions');
const { <PERSON><PERSON><PERSON> } = require('buffer');
const userHandler = require('../handlers/user-handler');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('usersignup', {
    methods: ['POST'],
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);
        const authHeader = req.headers.get('authorization')
        if (!authHeader || !authHeader.startsWith('Basic ')) {
            return jsonResponse(`Authentication required`, HttpStatusCode.Unauthorized)
        }
        const base64Credentials = authHeader.split(' ')[1];
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
        const [username, password] = credentials.split(':');

        const validUsername = 'emr_integration';
        const validPassword = 'Letmegetin@2436';
        if (username === validUsername && password === validPassword) {
            if (!req.body) {
                return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest);
            }
            var data = await req.json();

            context.log(`User registration: ${JSON.stringify(data)}`)
            data.userRole = null;

            var res = await userHandler.createUser(data);

            context.log(`User created to cosmosDb ${JSON.stringify(res)}`);
            return jsonResponse(res);

        } else {
            context.log(`Username or Password is not correct`);
            return jsonResponse(`username or password is not correct`, HttpStatusCode.Unauthorized);
        }
    }
});
