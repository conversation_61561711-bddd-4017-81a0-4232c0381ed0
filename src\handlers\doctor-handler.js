const { encryptData } = require('../common/helper')
const logging = require('../common/logging')
const DoctorProfileModel = require('../models/doctor-model')
const doctorService = require('../services/doctor-service')
const userService = require('../services/user-service')
const { v4: uuidv4 } = require('uuid')

class DoctorHandler {
  async getDoctorById(id) {
    try {
      logging.logInfo('Get doctor by ID: ' + id)
      var doctor = await doctorService.getDoctor(id)
      return doctor
    } catch (error) {
      logging.logError('Unable to get doctor with ID ' + id, error)
      return null
    }
  }

  async getDoctorByEmail(email) {
    email = encryptData(email)
    logging.logInfo(`Get doctor by Email: ${email}`)
    const query = `SELECT * FROM c WHERE c.username = '${email}'`
    const data = await doctorService.queryDoctors(query)
    return data
  }

  async getDoctorByDoctorId(doctorId) {
    try {
      logging.logInfo('Get doctor by doctor ID: ' + doctorId)
      var query = `SELECT * FROM c WHERE c.id = '${doctorId}';`
      var data = await doctorService.queryDoctors(query)
      return data
    } catch (error) {
      logging.logError('Unable to get doctor with doctorId ' + doctorId, error)
      return null
    }
  }

  async getAllDoctors(pageSize, continueToken) {
    try {
      logging.logInfo(
        `Get all doctor pageSize = ${pageSize}, continueToken = ${continueToken}`,
      )
      var data = await doctorService.getAllDoctors(pageSize, continueToken)
      return data
    } catch (error) {
      logging(error)
      return null
    }
  }

  async updateDoctor(doctor, updated_by) {
    try {
      logging.logInfo(`Update doctor : `, JSON.stringify(doctor))
      doctor.updated_by = updated_by
      var result = await doctorService.updateDoctor(doctor)
      return result
    } catch (error) {
      logging(error)
      return null
    }
  }

  async upsertDoctor(id, doctor, updated_by) {
    logging.logInfo(`Upsert doctor : ${JSON.stringify(doctor)}`)
    doctor.updated_by = updated_by

    const existingDoctor = await doctorService.getDoctor(id)
    if (!existingDoctor) {
      logging.logError(`Doctor with ID ${id} not found`)
      return null
    }

    if (doctor?.general?.fullName) {
      doctor.name = doctor.general.fullName
    }

    var result = await doctorService.upsertDoctor(id, doctor)

    // Update corresponding user using username as email
    if (existingDoctor.username) {
      const userData = {
        name: doctor.name,
      }
      await userService.updateUserByEmail(existingDoctor.username, userData)
    }

    return result
  }

  async getDoctorByQuery(query, pageSize, continueToken) {
    try {
      var data = await doctorService.queryDoctors(
        query,
        pageSize,
        continueToken,
      )
      return data
    } catch (error) {
      logging(error)
      return null
    }
  }

  async deleteDoctor(id) {
    try {
      logging.logInfo(`Delete doctor with ID: ${id}`)
      var result = await doctorService.deleteDoctor(id)
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async createDoctor(doctor, created_by) {
    try {
      // if (!doctor.id) {
      //     doctor.id = generateDoctorId();
      // }
      var doctorData = new DoctorProfileModel(doctor)
      if (doctorData.general.doctorID == null) {
        doctorData.general.doctorID = generateDoctorId()
      }
      doctorData.id = doctorData.general.doctorID
      doctorData.created_by = created_by
      doctorData.updated_by = created_by
      var result = await doctorService.createDoctor(doctorData)
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async updateDoctorPatients(doctorpatients, updated_by) {
    try {
      doctorpatients.updated_by = updated_by
      var result = await doctorService.updateDoctorPatient(doctorpatients)
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async createDoctorSummary(doctorSummary, created_by) {
    try {
      doctorSummary.id = uuidv4()
      doctorSummary.created_by = created_by
      doctorSummary.updated_by = created_by
      var result = await doctorService.createDoctorSumamry(doctorSummary)
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async updateDoctorSummary(id, doctorSummary, updated_by) {
    try {
      doctorSummary.updated_by = updated_by
      var result = await doctorService.upsertDoctorSummary(id, doctorSummary)
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async getDoctorSummary(patientId) {
    try {
      var data = await doctorService.getDoctorSummary(patientId)
      return data
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async getDoctorCustomiseEmr(doctorId) {
    try {
      var data = await doctorService.getDoctorCustomiseEmr(doctorId)
      return data
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async createDoctorCustomiseEmr(doctorCustomiseEmr, created_by) {
    try {
      doctorCustomiseEmr.id = uuidv4()
      doctorCustomiseEmr.created_by = created_by
      doctorCustomiseEmr.updated_by = created_by
      var result = await doctorService.createDoctorCustomiseEmr(
        doctorCustomiseEmr,
      )
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }

  async patchDoctorCustomiseEmr(id, doctorCustomiseEmr, updated_by) {
    try {
      doctorCustomiseEmr.updated_by = updated_by
      var result = await doctorService.patchDoctorCustomiseEmr(
        id,
        doctorCustomiseEmr,
      )
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }
}

function generateDoctorId() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let doctorId = ''
  for (let i = 0; i < 8; i++) {
    doctorId += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return doctorId
}
module.exports = new DoctorHandler()
