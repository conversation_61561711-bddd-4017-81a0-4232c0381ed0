const CosmosDbMetadata = require("../models/CosmosDb-Metadata-model")

class DoctorProfileModel extends CosmosDbMetadata {
    constructor(data) {
        super(data);
        this.id = data.id || "";
        this.username = data.username || "";
        this.general = {
            fullName: data.general?.fullName || "",
            designation: data.general?.designation || "",
            department: data.general?.department || "",
            doctorID: data.general?.doctorID || "",
            contactNumber: data.general?.contactNumber || "",
            workEmail: data.general?.workEmail || ""
        };
        this.personal = {
            age: data.personal?.age || null,
            bloodGroup: data.personal?.bloodGroup || "",
            height: data.personal?.height || null,
            weight: data.personal?.weight || null,
            isPersonWithDisability: data.personal?.isPersonWithDisability || false,
            percentOfDisability: data.personal?.percentOfDisability || null,
            identificationMark: data.personal?.identificationMark || "",
            maritalStatus: data.personal?.maritalStatus || "",
            dateOfWedding: data.personal?.dateOfWedding || "",
            nationality: data.personal?.nationality || "",
            religion: data.personal?.religion || "",
            caste: data.personal?.caste || "",
            category: data.personal?.category || "",
            reservationDetails: data.personal?.reservationDetails || "",
            idProof: {
                type: data.personal?.idProof?.type || "",
                number: data.personal?.idProof?.number || "",
                description: data.personal?.idProof?.description || "",
                url: data.personal?.idProof?.url || ""
            },
            hometownDetails: {
                hometown: data.personal?.hometownDetails?.hometown || "",
                state: data.personal?.hometownDetails?.state || "",
                district: data.personal?.hometownDetails?.district || "",
                country: data.personal?.hometownDetails?.country || ""
            },
            birthDetails: {
                placeOfBirth: data.personal?.birthDetails?.placeOfBirth || "",
                state: data.personal?.birthDetails?.state || "",
                district: data.personal?.birthDetails?.district || "",
                country: data.personal?.birthDetails?.country || ""
            },
            address: {
                permanent: {
                    home: data.personal?.address?.permanent?.home || "",
                    street: data.personal?.address?.permanent?.street || "",
                    city: data.personal?.address?.permanent?.city || "",
                    pinCode: data.personal?.address?.permanent?.pinCode || "",
                    district: data.personal?.address?.permanent?.district || "",
                    state: data.personal?.address?.permanent?.state || "",
                    country: data.personal?.address?.permanent?.country || "",
                    phone: data.personal?.address?.permanent?.phone || "",
                    mobile: data.personal?.address?.permanent?.mobile || "",
                    email: data.personal?.address?.permanent?.email || "",
                    proof: {
                        description: data.personal?.address?.permanent?.proof?.description || "",
                        url: data.personal?.address?.permanent?.proof?.url || ""
                    }
                },
                current: {
                    home: data.personal?.address?.current?.home || "",
                    street: data.personal?.address?.current?.street || "",
                    city: data.personal?.address?.current?.city || "",
                    pinCode: data.personal?.address?.current?.pinCode || "",
                    district: data.personal?.address?.current?.district || "",
                    state: data.personal?.address?.current?.state || "",
                    country: data.personal?.address?.current?.country || "",
                    phone: data.personal?.address?.current?.phone || "",
                    mobile: data.personal?.address?.current?.mobile || "",
                    email: data.personal?.address?.current?.email || "",
                    proof: {
                        description: data.personal?.address?.current?.proof?.description || "",
                        url: data.personal?.address?.current?.proof?.url || ""
                    }
                }
            }
        };
        this.emergencyContacts = data.emergencyContacts || [];
        this.professionalDetails = {
            medicalRegistration: {
                councilName: data.professionalDetails?.medicalRegistration?.councilName || "",
                registrationNumber: data.professionalDetails?.medicalRegistration?.registrationNumber || "",
                validFrom: data.professionalDetails?.medicalRegistration?.validFrom || "",
                validTo: data.professionalDetails?.medicalRegistration?.validTo || "",
                proof: {
                    description: data.professionalDetails?.medicalRegistration?.proof?.description || "",
                    url: data.professionalDetails?.medicalRegistration?.proof?.url || ""
                }
            },
            specialties: data.professionalDetails?.specialties || [],
            qualifications: data.professionalDetails?.qualifications || [],
            certifications: data.professionalDetails?.certifications || [],
            experience: data.professionalDetails?.experience || []
        };
        this.family = data.family || [];
        this.languagesKnown = data.languagesKnown || [];
        this.bankDetails = data.bankDetails || [];
        this.insurance = data.insurance || [];
        this.researchAndPublications = data.researchAndPublications || [];
        this.affiliations = data.affiliations || [];
        this.documents = data.documents || {};
    }
}

module.exports = DoctorProfileModel;
