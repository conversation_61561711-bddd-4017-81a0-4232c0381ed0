const { HttpResponse } = require('@azure/functions');
const crypto = require('crypto');
// Define a static secret key (32 bytes for AES-256, store securely in ENV or Secrets Manager)
const secretKey = Buffer.from('a1b2c3d4e5f67890a1b2c3d4e5f67890', 'utf8'); // 32 characters
const iv = Buffer.alloc(16, 0); // Fixed 16-byte IV for deterministic encryption

const helper = {
    parseJSON: (jsonString) => {
        try {
            if (typeof (jsonString) == "object") {
                return jsonString;
            }
            var obj = JSON.parse(jsonString);
            return obj;
        } catch (error) {
            console.error("Unable to parse json string to object :: " + jsonString, error);
            return '';
        }
    },

    validateMedicalRecord: (obj) => {
        const summaryInfo = process.env.SummaryInfo;
        const newKeysArray = summaryInfo.split(',').map(key => key.trim());
        // const requiredKeys  = ["presentingcomplaint", "pasthistory", "familyhistory", "socialhistory", "situationalhistory", "habitualhistory", "diagnosis", "recommendations"];
        return newKeysArray.every(key => key in obj);
    },

    jsonResponse: (bodyContent, statusCode = 200) => {
        const response = new HttpResponse({ jsonBody: bodyContent, status: statusCode });
        response.headers.set('content-type', 'application/json');
        return response;
    },
    encryptData: (data) => {
        const cipher = crypto.createCipheriv('aes-256-cbc', secretKey, iv);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    },
    decryptData: (data) => {
        const decipher = crypto.createDecipheriv('aes-256-cbc', secretKey, iv);
        let decrypted = decipher.update(data, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }
}

module.exports = helper;