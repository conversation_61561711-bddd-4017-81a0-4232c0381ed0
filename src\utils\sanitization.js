/**
 * Sanitize input values by removing double quotes.
 * @param {string} value - The input value to sanitize.
 * @returns {string|null} - Sanitized value or null if input is falsy.
 */
function sanitizeInput(value) {
  return value ? value.replace(/"/g, '') : null
}

/**
 * Escape special characters for Cosmos DB queries.
 * This function escapes characters that have special meaning in Cosmos DB SQL queries.
 * @param {string} value - The input value to escape.
 * @returns {string} - Escaped value safe for use in Cosmos DB queries.
 */
function escapeForCosmosDB(value) {
  if (!value) return ''

  // Escape single quotes by doubling them
  // Escape backslashes
  // Remove or escape other potentially problematic characters
  return value
    .replace(/\\/g, '\\\\') // Escape backslashes first
    .replace(/'/g, "''") // Escape single quotes
    .replace(/"/g, '\\"') // Escape double quotes
  // Note: We don't escape parentheses as they should work fine in CONTAINS()
}

/**
 * Sanitize search text specifically for LOINC/lab test searches.
 * Removes dangerous characters while preserving meaningful search terms.
 * @param {string} searchText - The search text to sanitize.
 * @returns {string|null} - Sanitized search text or null if input is falsy.
 */
function sanitizeSearchText(searchText) {
  if (!searchText) return null

  // For medical/LOINC terms, be more permissive to preserve meaningful search
  // Allow: letters, numbers, spaces, parentheses, hyphens, underscores, periods, plus signs, forward slashes
  // Remove only the most dangerous characters that could break SQL queries
  const cleaned = searchText
    .replace(/['"\\;]/g, '') // Remove quotes, backslashes, semicolons
    .trim()

  return cleaned || null
}

module.exports = {
  sanitizeInput,
  escapeForCosmosDB,
  sanitizeSearchText,
}
