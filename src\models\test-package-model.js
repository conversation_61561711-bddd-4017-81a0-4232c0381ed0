const { PackageType } = require('../common/constant')
const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class TestPackageModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.name = data.name || ''

    if (!Object.values(PackageType).includes(data.type)) {
      throw new Error(
        `Invalid package type. Allowed types are ${Object.values(
          PackageType,
        ).join(', ')}`,
      )
    }
    this.type = data.type || PackageType.DEPARTMENT
    this.tests = data.tests || []
    this.userId = data.userId || ''
  }
}

module.exports = TestPackageModel
