const { app, HttpResponse } = require('@azure/functions')
const AuthMessage = require('../common/auth-message')
const { doValidate } = require('../common/user-validation')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const medicineHandler = require('../handlers/medicine-handler')
const finalizePatientRecordsCron = require('../tasks/finalize-patient-history-cron')
const testHandler = require('../handlers/lab-test-handler')
const finalizePatientLifestyleCron = require('../tasks/finalize-records-cron')
const { DefaultRoles } = require('../common/roles')
const userHandler = require('../handlers/user-handler')
const emailService = require('../services/email-service')
const b2cService = require('../services/b2c-service')
const userRepository = require('../repositories/admin/user-repository')

finalizePatientRecordsCron()
finalizePatientLifestyleCron()
app.setup({
  enableHttpStream: false,
})

app.hook.appStart(async (context) => {
  console.log('EMR Function start...')
  // try {
  //   const superAdminEmail = '<EMAIL>'
  //   const superAdminPassword = 'SuperAdminArcaaiEHR@123'

  //   const existingSuperAdmins = await userRepository.getUserByRole(
  //     DefaultRoles.SUPER_ADMIN,
  //   )
  //   if (existingSuperAdmins && existingSuperAdmins.length > 0) {
  //     console.log(
  //       'Super Admin already exists in the database. Skipping creation.',
  //     )
  //   } else {
  //     try {
  //       const superAdminObj = {
  //         userType: DefaultRoles.SUPER_ADMIN,
  //         userRole: DefaultRoles.SUPER_ADMIN,
  //         name: 'Super Admin Arcaai EHR',
  //         email: superAdminEmail,
  //         isActive: true,
  //         isOrganizationMainAdmin: true,
  //       }

  //       const result = await userHandler.createUser(superAdminObj, 'system')

  //       if (result) {
  //         console.log('Super Admin seeded successfully.')
  //       } else {
  //         console.warn('Super Admin user creation returned null.')
  //       }
  //     } catch (error) {
  //       if (
  //         error.message?.includes('userPrincipalName already exists') ||
  //         error.code === 'USER_ALREADY_EXISTS'
  //       ) {
  //         console.warn('B2C user already exists, checking local DB...')

  //         const existingUser = await userHandler.getUserByEmail(superAdminEmail)
  //         if (!existingUser || existingUser.length === 0) {
  //           console.log('Creating local user without B2C...')

  //           const localUser = {
  //             userType: DefaultRoles.SUPER_ADMIN,
  //             userRole: DefaultRoles.SUPER_ADMIN,
  //             name: 'Super Admin Arcaai EHR',
  //             email: superAdminEmail,
  //             isActive: true,
  //             b2cUserId: null,
  //           }

  //           await userService.addUser(localUser)
  //           console.log('Local DB user created.')
  //         } else {
  //           console.log('Both B2C and local DB users already exist.')
  //         }
  //       } else {
  //         console.error('Failed to create Super Admin:', error)
  //         throw error
  //       }
  //     }
  //   }

  //   setImmediate(async () => {
  //     try {
  //       // const seedResult = await medicineHandler.seedMedicinesFromExcel()
  //       // console.log(seedResult.message)
  //       // const result = await testHandler.seedTestsFromExcel()
  //       // console.log(result.message)
  //     } catch (error) {
  //       console.error('Error while seeding medicines/test:', error.message)
  //     }
  //   })
  // } catch (error) {
  //   console.error('Error during app startup:', error.message)
  // }
  app.setup({
    enableHttpStream: false,
  })
})

app.hook.appTerminate((context) => {
  console.log('EMR Function stop...')
})

app.hook.preInvocation(async (context) => {
  var req = context.inputs[0]
  const auth = await doValidate(req)
  if (auth.message != AuthMessage.SUCCESS) {
    context.functionHandler = (...args) => {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
  } else {
    context.invocationContext.extraInputs.set('decode', auth.decode)
  }
})
