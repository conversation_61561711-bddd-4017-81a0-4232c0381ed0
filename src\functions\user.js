const { app } = require('@azure/functions')
const userHandler = require('../handlers/user-handler')
const { HttpMethod } = require('../common/constant')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { logError } = require('../common/logging')
const auditLogger = require('../common/audit-logger')
app.http('user', {
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      context.log(`Http function processed req for url "${req.url}"`)
      const decode = context.extraInputs.get('decode')
      switch (req.method) {
        case HttpMethod.get:
          var data = null
          let email = req.query.get('email')
          const userType = req.query.get('userType')

          if (email && email.includes(' ')) {
            const emailPattern = /^[^\s@]+(\s[^\s@]*)*@[^\s@]+\.[^\s@]+$/
            if (emailPattern.test(email)) {
              email = email.replace(/ /g, '+')
            }
          }

          if (email) {
            data = await userHandler.getUserDetailsByEmail(email)
            if (!data || data.length === 0) {
              return jsonResponse(
                'User does not exist',
                HttpStatusCode.NotFound,
              )
            }
          } else if (userType) {
            if (userType === 'doctor') {
              data = await userHandler.getActiveDoctorsByUserOrganization(
                decode.oid,
              )
            } else {
              data = await userHandler.getUserByUserType(userType)
            }
          } else {
            return jsonResponse(
              `Missing email or userType`,
              HttpStatusCode.BadRequest,
            )
          }
          return jsonResponse(data)
        case HttpMethod.post:
          if (!req.body) {
            return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
          }
          const userPayload = await req.json()

          const pagesize = userPayload.pagesize
          const continuetoken = userPayload.continuetoken
          if (pagesize) {
            const data = await userHandler.getAllUser(pagesize, continuetoken)
            const count = await userHandler.getUserCount()
            data.total = count[0]
            return jsonResponse(data)
          }

          try {
            const userobj = {
              userRole: `${userPayload.userRole}`,
              userType: `${userPayload.userType}`,
              name: userPayload.name,
              email: userPayload.email,
              organizationId: userPayload.organizationId || null,
              roleId: userPayload.roleId || null,
              organizationName: userPayload.organizationName || null,
            }

            // Create user (this will handle both local DB and B2C creation + email)
            const data = await userHandler.createUser(userobj, decode.oid)
            if (data) {
              return jsonResponse(data)
            } else {
              return jsonResponse(
                'Failed to create user',
                HttpStatusCode.InternalServerError,
              )
            }
          } catch (error) {
            logError('Error creating user:', error)

            // Check for GraphError patterns in the error message (even if wrapped)
            const errorMessage = error.message || ''
            const isUserAlreadyExists =
              errorMessage.includes('userPrincipalName already exists') ||
              errorMessage.includes(
                'Another object with the same value for property userPrincipalName already exists',
              ) ||
              errorMessage.includes('already exists in AAD') ||
              errorMessage.includes('already exists')

            // Handle GraphError and Microsoft Graph API errors specifically
            if (
              error.name === 'GraphError' ||
              error.code === 'Request_BadRequest'
            ) {
              if (
                error.statusCode === 409 ||
                error.statusCode === 400 ||
                isUserAlreadyExists
              ) {
                return jsonResponse(
                  `User with email ${userPayload.email} already exists. Please use a different email address.`,
                  HttpStatusCode.Conflict,
                )
              }

              // Other GraphError cases
              return jsonResponse(
                'Failed to create user account due to authentication service error. Please try again.',
                HttpStatusCode.InternalServerError,
              )
            }

            // Handle error messages that indicate user already exists (even if not GraphError)
            if (isUserAlreadyExists) {
              return jsonResponse(
                `User with email ${userPayload.email} already exists. Please use a different email address.`,
                HttpStatusCode.Conflict,
              )
            }

            // Handle wrapped GraphError messages
            if (
              errorMessage.includes('Failed to create B2C user') &&
              isUserAlreadyExists
            ) {
              return jsonResponse(
                `User with email ${userPayload.email} already exists. Please use a different email address.`,
                HttpStatusCode.Conflict,
              )
            }

            if (errorMessage.includes('Failed to create B2C user')) {
              return jsonResponse(
                'Failed to create user account. Please try again later.',
                HttpStatusCode.InternalServerError,
              )
            }

            // Generic error response
            context.log('Unexpected error during user creation:', error)
            return jsonResponse(
              'An unexpected error occurred while creating the user. Please try again later.',
              HttpStatusCode.InternalServerError,
            )
          }
        case HttpMethod.patch:
          const userId = req.query.get('userId')

          if (!userId) {
            return jsonResponse(
              'Missing userId query parameter',
              HttpStatusCode.BadRequest,
            )
          }

          if (!req.body) {
            return jsonResponse('Missing payload', HttpStatusCode.BadRequest)
          }

          const updatePayload = await req.json()

          if (updatePayload.email) {
            try {
             await userHandler.updateUserEmail(
                userId,
                updatePayload.email,
                decode.oid,
              )
            } catch (error) {
              context.log('Error updating user email:', error)
              return jsonResponse(
                'Error updating user email',
                HttpStatusCode.InternalServerError,
              )
            }
          }

          if (updatePayload.organizationId) {
            return jsonResponse(
              'Organization cannot be changed. Please contact administrator.',
              HttpStatusCode.BadRequest,
            )
          }

          if (Object.keys(updatePayload).length === 0) {
            return jsonResponse(
              'No fields provided for update',
              HttpStatusCode.BadRequest,
            )
          }

          updatePayload.updated_by = decode.oid
          updatePayload.updated_on = new Date().toISOString()

          try {
            const result = await userHandler.updateUser(
              userId,
              updatePayload,
              decode.oid,
            )

            if (result) {
              await auditLogger.logAction('User Updated', decode.oid, {
                userId: userId,
                updatedFields: Object.keys(updatePayload),
              })
              return jsonResponse(result)
            } else {
              return jsonResponse('User not found', HttpStatusCode.NotFound)
            }
          } catch (error) {
            context.log('Error updating user:', error)
            return jsonResponse(
              'Error updating user',
              HttpStatusCode.InternalServerError,
            )
          }
        case HttpMethod.delete:
          const deleteUserId = req.query.get('userId')

          if (!deleteUserId) {
            return jsonResponse(
              'Missing userId query parameter',
              HttpStatusCode.BadRequest,
            )
          }

          try {
            const result = await userHandler.deleteUser(
              deleteUserId,
              decode.oid,
            )

            if (result.success) {
              await auditLogger.logAction('User Deleted', decode.oid, {
                userId: deleteUserId,
                deletedUser: result.deletedUser,
              })
              return jsonResponse({
                message: 'User deleted successfully',
                userId: deleteUserId,
              })
            } else {
              return jsonResponse(
                result.message,
                result.statusCode || HttpStatusCode.InternalServerError,
              )
            }
          } catch (error) {
            context.log('Error deleting user:', error)
            return jsonResponse(
              'Error deleting user',
              HttpStatusCode.InternalServerError,
            )
          }
        default:
          return jsonResponse(
            `Unsupported HTTP method`,
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (topLevelError) {
      context.log('Top-level error in user handler:', topLevelError)
      logError('Top-level error in user handler:', topLevelError)

      // Handle GraphError at top level as fallback
      if (
        topLevelError.name === 'GraphError' &&
        topLevelError.message &&
        topLevelError.message.includes('userPrincipalName already exists')
      ) {
        return jsonResponse(
          'User with this email already exists. Please use a different email address.',
          HttpStatusCode.Conflict,
        )
      }

      return jsonResponse(
        'An unexpected error occurred. Please try again later.',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('set-password', {
  methods: ['POST'],
  route: 'user/set-password',
  authLevel: 'function',
  handler: async (req, context) => {
    return userHandler.setPassword(req)
  },
})

app.http('user-list', {
  methods: ['GET'],
  route: 'user/list',
  authLevel: 'function',
  handler: async (req, context) => {
    return userHandler.getUsersByOrganization(req)
  },
})
