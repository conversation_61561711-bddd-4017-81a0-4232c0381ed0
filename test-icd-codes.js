// Test script to verify ICD codes
const axios = require('axios')

const testCodes = [
  'MG26',     // From fever search - should work
  '1D81.Z',   // From fever search - should work  
  '4A60.0',   // From fever search - should work
  'A14',      // From diabetes search - should work
  'IB94.Z',   // The problematic code
  'CA22.Z',   // From your example
  'PB28'      // From your example
]

async function testIcdCode(code) {
  try {
    console.log(`\n=== Testing ICD Code: ${code} ===`)
    
    const response = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: code },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )
    
    console.log(`Results: ${response.data?.destinationEntities?.length || 0} entities`)
    
    if (response.data?.destinationEntities?.length > 0) {
      response.data.destinationEntities.forEach((entity, index) => {
        console.log(`  ${index + 1}. Code: ${entity.theCode}, Title: ${entity.title}`)
      })
    } else {
      console.log(`  No results found for ${code}`)
    }
    
  } catch (error) {
    console.log(`  Error searching for ${code}: ${error.message}`)
  }
}

async function runTests() {
  console.log('Testing ICD Code Search...')
  
  for (const code of testCodes) {
    await testIcdCode(code)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second between requests
  }
  
  console.log('\n=== Test Complete ===')
}

runTests()
