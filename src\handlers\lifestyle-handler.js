const lifeStyleService = require('../services/lifestyle-service');
const { logError, logInfo } = require('../common/logging');
const { v4: uuidv4 } = require('uuid');

class LifeStyleHandler {

    async createLifeStyle(lifeStyle, created_by) {
        try {
            logInfo(`Create lifeStyle : `, JSON.stringify(lifeStyle));
            lifeStyle.created_by = created_by;
            lifeStyle.updated_by = created_by;
            if (!lifeStyle.id) {
                lifeStyle.id = uuidv4();
            }
            var result = await lifeStyleService.createLifeStyle(lifeStyle);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getLifeStyleByQuery(query, pageSize, continueToken) {
        try {
            logInfo(`Get lifeStyle by query : ${query}`);
            var result = await lifeStyleService.getLifeStyleByQuery(query, pageSize, continueToken);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getLifeStyeBySourceName(sourceName) {
        try {
            logInfo(`Get lifeStyle by sourceName : ${sourceName}`);
            var result = await lifeStyleService.getLifeStyeBySourceName(sourceName);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async patchLifeStyle(id, patchPayload, updated_by) {
        try {
            logInfo(`Patch lifeStyle : ${id} :: ${JSON.stringify(patchPayload)}`);
            patchPayload.updated_by = updated_by;
            var result = await lifeStyleService.patchLifeStyle(id, patchPayload);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getLifeStyeBySourceNameAndSession(sourceName, section) {
        try {
            logInfo(`Get lifeStyle by sourceName : ${sourceName} and section : ${section}`);
            var query = `SELECT * FROM c WHERE c.source = '${sourceName}' AND c.section_id = '${section}'`;
            var result = await lifeStyleService.getLifeStyleByQuery(query);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getLatestSession(sourceName) {
        try {
            logInfo(`Get latest session by sourceName : ${sourceName}`);
            var query = `SELECT DISTINCT VALUE ARRAY_SLICE(c.sections, -1)[0] FROM c WHERE c.source = '${sourceName}'`;
            var result = await lifeStyleService.getLifeStyleByQuery(query);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async deleteLifeStyle(id) {
        try {
            logInfo(`Delete lifeStyle : ${id}`);
            var result = await lifeStyleService.deleteLifeStyle(id);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

}

module.exports = new LifeStyleHandler();