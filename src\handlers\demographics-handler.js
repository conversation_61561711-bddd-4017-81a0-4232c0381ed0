const demographicsService = require('../services/demographics-service');
const logging = require('../common/logging');
const DemographicsModel = require('../models/demographics-model');
const { v4: uuidv4 } = require('uuid');

class DemographicsHandler {

    async getDemographics(patientId) {
        try {
            return await demographicsService.getDemographicsByPatientId(patientId);
        } catch (error) {
            logging.logError(`Unable to get demographics for patient ${patientId}`, error);
            throw error;
        }
    }

    async createDemographics(patientId, demographicsData, createdBy) {
        try {
            // Create demographics model and validate
            const demographics = new DemographicsModel({
                ...demographicsData,
                patientId: patientId,
                id: uuidv4(),
                create_by: createdBy,
                update_by: createdBy
            });

            // Validate the data
            const validationErrors = demographics.validate();
            if (validationErrors.length > 0) {
                throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
            }

            // Check if demographics already exists for this patient
            const existingDemographics = await demographicsService.getDemographicsByPatientId(patientId);
            if (existingDemographics) {
                throw new Error('Demographics already exists for this patient');
            }

            return await demographicsService.createDemographics(demographics);
        } catch (error) {
            logging.logError(`Unable to create demographics for patient ${patientId}`, error);
            throw error;
        }
    }

    async updateDemographics(demographicsId, demographicsData, updatedBy) {
        try {
            // Get existing demographics
            const existingDemographics = await demographicsService.getDemographicsById(demographicsId);
            if (!existingDemographics) {
                throw new Error('Demographics record not found');
            }

            // Create updated demographics model
            const updatedDemographics = new DemographicsModel({
                ...existingDemographics,
                ...demographicsData,
                id: demographicsId,
                update_by: updatedBy
            });

            // Validate the updated data
            const validationErrors = updatedDemographics.validate();
            if (validationErrors.length > 0) {
                throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
            }

            return await demographicsService.updateDemographics(updatedDemographics);
        } catch (error) {
            logging.logError(`Unable to update demographics for id ${demographicsId}`, error);
            throw error;
        }
    }
}

module.exports = new DemographicsHandler();