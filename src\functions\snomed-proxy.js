const axios = require('axios')
const axiosRetry = require('axios-retry').default
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

axiosRetry(axios, {
  retries: 3,
  retryDelay: (retryCount, error) => {
    if (error?.response?.status === 429) {
      return 5000 // wait 5 seconds before retry on 429
    }
    return axiosRetry.exponentialDelay(retryCount)
  },
  retryCondition: (error) => {
    return (
      axiosRetry.isNetworkOrIdempotentRequestError(error) ||
      error.response?.status === 429
    )
  },
})

app.http('snomed-proxy', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'snomed-proxy',
  handler: async (request, context) => {
    const term = request.query.get('term')
    const searchType = request.query.get('type') || 'auto' // auto, disease, code

    if (!term) {
      return jsonResponse({ error: 'Missing "term" parameter' }, 400)
    }

    try {
      // Determine if the query looks like a SNOMED code (numeric)
      const isSnomedCode = /^\d+$/.test(term.trim())

      let searchResults = []

      // Try different search strategies based on query type
      if (searchType === 'code' || (searchType === 'auto' && isSnomedCode)) {
        // Search by SNOMED code
        searchResults = await searchBySnomedCode(term)
      }

      if (searchResults.length === 0 && (searchType === 'disease' || searchType === 'auto')) {
        // Search by disease name
        searchResults = await searchByDiseaseName(term)
      }

      return jsonResponse({
        query: term,
        searchType: isSnomedCode ? 'code' : 'disease',
        results: searchResults,
        total: searchResults.length
      })

    } catch (error) {
      console.error('Error in SNOMED search:', error.message)
      const statusCode = error.response?.status || 500
      const message =
        statusCode === 429
          ? 'Rate limit exceeded, please try again later.'
          : 'Error fetching SNOMED data'

      return jsonResponse({ error: message, details: error.message }, statusCode)
    }
  },
})

// Search by SNOMED code
async function searchBySnomedCode(code) {
  try {
    // Get concept by SNOMED code
    const conceptResponse = await axios.get(
      `https://browser.ihtsdotools.org/snowstorm/snomed-ct/browser/MAIN/concepts/${code}`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; MyApp/1.0; +http://myappdomain.com)',
        },
        timeout: 10000,
      }
    )

    if (conceptResponse.data) {
      const concept = conceptResponse.data
      return [await formatSnomedResult(concept, 'exact_match')]
    }
  } catch (error) {
    console.log(`Exact SNOMED code lookup failed for ${code}, trying search...`)
  }

  // If exact lookup fails, try search
  return await searchByDiseaseName(code)
}

// Search by disease name
async function searchByDiseaseName(diseaseName) {
  try {
    const response = await axios.get(
      'https://browser.ihtsdotools.org/snowstorm/snomed-ct/browser/MAIN/descriptions',
      {
        params: {
          term: diseaseName,
          active: true,
          limit: 20
        },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; MyApp/1.0; +http://myappdomain.com)',
        },
        timeout: 10000,
      }
    )

    if (response.data && response.data.items) {
      const results = []
      for (const item of response.data.items.slice(0, 10)) { // Limit to 10 results
        const formattedResult = await formatSnomedResult(item.concept, 'search_result')
        if (formattedResult) {
          results.push(formattedResult)
        }
      }
      return results
    }

    return []
  } catch (error) {
    console.error('Disease name search failed:', error.message)
    return []
  }
}

// Format SNOMED result and try to get ICD mapping
async function formatSnomedResult(concept, type) {
  if (!concept) return null

  let icdCode = ''

  // Try to get ICD mapping for this SNOMED concept
  try {
    const mappingResponse = await axios.get(
      `https://browser.ihtsdotools.org/snowstorm/snomed-ct/browser/MAIN/concepts/${concept.conceptId}/authoring-form`,
      {
        headers: {
          Accept: 'application/json',
          'User-Agent': 'Mozilla/5.0 (compatible; MyApp/1.0; +http://myappdomain.com)',
        },
        timeout: 5000,
      }
    )

    // Look for ICD mappings in the response
    if (mappingResponse.data && mappingResponse.data.classAxioms) {
      // This is a simplified approach - you might need to adjust based on actual API response
      icdCode = extractIcdFromMapping(mappingResponse.data)
    }
  } catch (error) {
    // ICD mapping lookup failed, continue without it
    console.log(`ICD mapping lookup failed for SNOMED ${concept.conceptId}`)
  }

  return {
    snomedCode: concept.conceptId,
    icdCode: icdCode,
    title: concept.pt?.term || concept.fsn?.term || 'Unknown',
    description: concept.definition || '',
    source: 'SNOMED CT',
    type: type,
    active: concept.active
  }
}

// Extract ICD code from mapping data (simplified)
function extractIcdFromMapping(mappingData) {
  // This is a placeholder - you'll need to adjust based on actual API response structure
  // The actual mapping structure depends on the SNOMED API response format
  try {
    if (mappingData.relationships) {
      for (const rel of mappingData.relationships) {
        if (rel.type && rel.type.pt && rel.type.pt.term.includes('ICD')) {
          return rel.target?.pt?.term || ''
        }
      }
    }
  } catch (error) {
    console.log('Error extracting ICD mapping:', error.message)
  }
  return ''
}
