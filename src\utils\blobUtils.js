const { BlobServiceClient } = require('@azure/storage-blob')

const connectionString = process.env.AzureWebJobsStorage || ''
const blobServiceClient =
  BlobServiceClient.fromConnectionString(connectionString)

/**
 * Get a container client.
 */
const getContainerClient = (containerName) =>
  blobServiceClient.getContainerClient(containerName)

/**
 * Find a blob by prefix.
 */
const findBlobByPrefix = async (containerName, prefix) => {
  const containerClient = getContainerClient(containerName)
  const blobs = containerClient.listBlobsFlat({ prefix })
  for await (const blob of blobs) {
    return blob.name
  }
  return null
}

/**
 * Delete blobs by prefix.
 */
const deleteBlobsByPrefix = async (containerName, prefix) => {
  const containerClient = getContainerClient(containerName)
  const blobs = containerClient.listBlobsFlat({ prefix })
  for await (const blob of blobs) {
    const blockBlobClient = containerClient.getBlockBlobClient(blob.name)
    await blockBlobClient.deleteIfExists()
  }
}

/**
 * Upload a blob.
 */
const uploadBlob = async (containerName, blobName, buffer) => {
  const containerClient = getContainerClient(containerName)
  await containerClient.createIfNotExists({ access: 'blob' })
  const blockBlobClient = containerClient.getBlockBlobClient(blobName)
  await blockBlobClient.uploadData(buffer)
  return blockBlobClient.url
}

module.exports = {
  getContainerClient,
  findBlobByPrefix,
  deleteBlobsByPrefix,
  uploadBlob,
}
