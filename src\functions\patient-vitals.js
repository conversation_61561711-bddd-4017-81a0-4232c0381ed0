const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { HttpMethod } = require('../common/constant')
const vitalsHandler = require('../handlers/vitals-handler')

app.http('patient-vitals', {
  methods: ['GET', 'POST', 'PUT'],
  authLevel: 'function',
  route: 'patient/vitals',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')
    const patientId = req.query.get('patientId')
    if (!patientId) {
      return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest)
    }

    switch (req.method) {
      case HttpMethod.get:
        var startDate = req.query.get('startdate')
        var endDate = req.query.get('enddate') || new Date().toISOString()

        if (!startDate) {
          var data = await vitalsHandler.getVitalsByPatient(patientId)
          return jsonResponse(data)
        } else {
          var data = await vitalsHandler.getVitalsbyDate(
            patientId,
            startDate,
            endDate,
          )
          return jsonResponse(data)
        }
      case HttpMethod.post:
      case HttpMethod.put:
        if (!req.body) {
          return jsonResponse(
            `Missing vitals payload`,
            HttpStatusCode.BadRequest,
          )
        }
        var vitals = await req.json()
        var data = null
        if (req.method == HttpMethod.post) {
          data = await vitalsHandler.CreateVitals(patientId, vitals, decode.oid)
          if (!data) {
            return jsonResponse(
              `Unable to create vitals`,
              HttpStatusCode.Forbidden,
            )
          }
        }
        if (req.method == HttpMethod.put) {
          data = await vitalsHandler.updateVitals(patientId, vitals, decode.oid)
          if (!data) {
            return jsonResponse(
              `Unable to update vitals`,
              HttpStatusCode.Forbidden,
            )
          }
        }
        return jsonResponse(data)
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
