const axios = require('axios')
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const version = request.query.get('version') || 'both' // 'icd10', 'icd11', or 'both'
    const searchType = request.query.get('type') || 'auto'

    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      console.log(`ICD Search - Query: "${query}", Version: ${version}, Type: ${searchType}`)

      // Auto-correct common formatting issues
      let correctedQuery = query.trim()

      // Fix common ICD-10 formatting issues
      // A001 -> A00.1, E111 -> E11.1, etc.
      if (/^[A-Z]\d{3}$/i.test(correctedQuery)) {
        const corrected = correctedQuery.substring(0, 3) + '.' + correctedQuery.substring(3)
        console.log(`Auto-correcting "${correctedQuery}" to "${corrected}"`)
        correctedQuery = corrected
      }

      // Determine if the query looks like an ICD code and which version
      const isIcdCode = /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(correctedQuery)
      const looksLikeIcd10 = /^[A-Z]\d{2}(\.\d+)?$/i.test(correctedQuery) // A15.0, E11.1, etc.
      const looksLikeIcd11 = /^[A-Z0-9]{2,4}(\.[A-Z0-9]+)?$/i.test(correctedQuery) && !looksLikeIcd10 // 5A11, MG26, etc.

      console.log(`Original: "${query}", Corrected: "${correctedQuery}"`)
      console.log(`Detected as ICD code: ${isIcdCode}, ICD-10 pattern: ${looksLikeIcd10}, ICD-11 pattern: ${looksLikeIcd11}`)

      let allResults = []

      // Strategy 1: If it looks like a specific version code, try that version first
      if (isIcdCode && (version === 'both' || version === 'icd10') && (looksLikeIcd10 || version === 'icd10')) {
        console.log('Trying ICD-10 API...')
        const icd10Results = await searchIcd10(correctedQuery, isIcdCode)
        if (icd10Results.length > 0) {
          allResults = allResults.concat(icd10Results)
          // If we found exact match in ICD-10, return immediately
          if (isIcdCode && icd10Results.some(r => r.theCode.toUpperCase() === correctedQuery.toUpperCase())) {
            console.log('Found exact match in ICD-10, returning immediately')
            return formatResponse(icd10Results.filter(r => r.theCode.toUpperCase() === correctedQuery.toUpperCase()), query, 'icd10')
          }
        }
      }

      if (isIcdCode && (version === 'both' || version === 'icd11') && (looksLikeIcd11 || version === 'icd11')) {
        console.log('Trying ICD-11 API...')
        const icd11Results = await searchIcd11(correctedQuery, isIcdCode)
        if (icd11Results.length > 0) {
          allResults = allResults.concat(icd11Results)
          // If we found exact match in ICD-11, return immediately
          if (isIcdCode && icd11Results.some(r => r.theCode.toUpperCase() === correctedQuery.toUpperCase())) {
            console.log('Found exact match in ICD-11, returning immediately')
            return formatResponse(icd11Results.filter(r => r.theCode.toUpperCase() === correctedQuery.toUpperCase()), query, 'icd11')
          }
        }
      }

      // Strategy 2: If no exact matches found or it's a disease name, search both versions
      if (allResults.length === 0 || !isIcdCode) {
        console.log('Searching both ICD versions...')

        if (version === 'both' || version === 'icd10') {
          const icd10Results = await searchIcd10(correctedQuery, isIcdCode)
          allResults = allResults.concat(icd10Results)
        }

        if (version === 'both' || version === 'icd11') {
          const icd11Results = await searchIcd11(correctedQuery, isIcdCode)
          allResults = allResults.concat(icd11Results)
        }
      }

      // Remove duplicates based on code
      const uniqueResults = allResults.filter((result, index, self) =>
        index === self.findIndex(r => r.theCode === result.theCode)
      )

      console.log(`Total unique results: ${uniqueResults.length}`)

      if (uniqueResults.length > 0) {
        return formatResponse(uniqueResults, query, version)
      }

      // No results found
      return jsonResponse({
        error: false,
        errorMessage: isIcdCode
          ? `ICD code "${query}" not found in ${version === 'both' ? 'ICD-10 or ICD-11' : version.toUpperCase()} database.`
          : `No results found for "${query}". Try a different search term.`,
        resultChopped: false,
        wordSuggestionsChopped: false,
        guessType: 0,
        uniqueSearchId: "",
        words: null,
        destinationEntities: []
      })

    } catch (error) {
      console.error('Error in ICD search:', error.message)
      if (error.response) {
        console.error('API Error Status:', error.response.status)
        console.error('API Error Data:', error.response.data)
      }

      return jsonResponse({
        error: 'Error fetching ICD data',
        details: error.message,
        query: query
      }, 500)
    }
  },
})

// Search ICD-10 using NLM API
async function searchIcd10(query, isIcdCode) {
  try {
    const response = await axios.get(
      'https://clinicaltables.nlm.nih.gov/api/icd10cm/v3/search',
      {
        params: {
          terms: query,
          sf: 'code,name',
          df: 'code,name',
          maxList: 25
        },
        timeout: 10000,
      }
    )

    console.log(`ICD-10 API: Found ${response.data[0]} total results, returning ${response.data[1]?.length || 0}`)

    if (response.data && response.data[1] && response.data[1].length > 0) {
      return response.data[3].map((item, index) => ({
        id: `icd10-${index}`,
        title: item[1] || 'Unknown',
        theCode: item[0] || '',
        definition: '',
        type: 'ICD-10-CM',
        chapter: '',
        score: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 1.0 : 0.8,
        isLeaf: true,
        source: 'ICD-10 (NLM)',
        version: 'ICD-10',
        matchType: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 'exact_code' : 'search_result'
      }))
    }

    return []
  } catch (error) {
    console.log(`ICD-10 API error: ${error.message}`)
    return []
  }
}

// Search ICD-11 using NLM API
async function searchIcd11(query, isIcdCode) {
  try {
    const response = await axios.get(
      'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
      {
        params: {
          terms: query,
          df: 'code,title,definition',
          maxList: 25
        },
        timeout: 10000,
      }
    )

    console.log(`ICD-11 API: Found ${response.data[0]} total results, returning ${response.data[1]?.length || 0}`)

    if (response.data && response.data[1] && response.data[1].length > 0) {
      return response.data[3].map((item, index) => ({
        id: `icd11-${index}`,
        title: item[1] || 'Unknown',
        theCode: item[0] || '',
        definition: item[2] || '',
        type: 'ICD-11',
        chapter: '',
        score: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 1.0 : 0.8,
        isLeaf: true,
        source: 'ICD-11 (NLM)',
        version: 'ICD-11',
        matchType: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 'exact_code' : 'search_result'
      }))
    }

    return []
  } catch (error) {
    console.log(`ICD-11 API error: ${error.message}`)
    return []
  }
}

// Format the final response
function formatResponse(results, query, version) {
  return jsonResponse({
    error: false,
    errorMessage: null,
    resultChopped: false,
    wordSuggestionsChopped: false,
    guessType: results.length === 1 ? 1 : 2,
    uniqueSearchId: "",
    words: null,
    query: query,
    version: version,
    destinationEntities: results
  })
}
