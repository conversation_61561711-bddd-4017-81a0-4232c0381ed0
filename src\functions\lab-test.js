const { app } = require('@azure/functions')
const labTestHandler = require('../handlers/lab-test-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')
const { sanitizeInput } = require('../utils/sanitization')

app.http('lab-test', {
  methods: ['GET', 'POST'],
  authLevel: 'function',
  route: 'lab-tests',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    switch (req.method) {
      case HttpMethod.get:
        try {
          const data = await labTestHandler.getAllLabTests()
          return jsonResponse(data)
        } catch (err) {
          return jsonResponse(
            'Error fetching lab tests',
            HttpStatusCode.InternalServerError,
          )
        }

      case HttpMethod.post:
        try {
          const result = await labTestHandler.addLabTest(req.body)
          return jsonResponse(result)
        } catch (err) {
          return jsonResponse(
            'Error adding lab test',
            HttpStatusCode.InternalServerError,
          )
        }

      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})

app.http('lab-test-search', {
  methods: ['POST'],
  route: 'lab-tests/search',
  authLevel: 'function',
  handler: (req, context) => labTestHandler.searchLabTests(req),
})

app.http('lab-test-departments', {
  methods: ['GET'],
  route: 'lab-test/departments',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.getDepartments(req)
    } catch (error) {
      context.log.error('Error fetching departments:', error)
      return jsonResponse('Internal server error', 500)
    }
  },
})

app.http('loinc-list', {
  methods: ['GET'],
  route: 'loinc/list',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const searchText = req.query.get('searchText') || ''
      const department = req.query.get('department') || 'ALL'
      const organizationId = req.query.get('organizationId')
      const status = req.query.get('status') || null // 'active', 'inactive', or null for all
      const pageSize = parseInt(req.query.get('pageSize')) || 100
      const page = parseInt(req.query.get('page')) || 1
      const continuationToken = req.query.get('continuationToken') || null

      const sanitizedSearchText = sanitizeInput(searchText)
      const sanitizedDepartment = sanitizeInput(department)
      const sanitizedStatus = sanitizeInput(status)

      const data = await labTestHandler.getLoincList(
        sanitizedSearchText,
        sanitizedDepartment,
        organizationId,
        pageSize,
        continuationToken,
        page,
        sanitizedStatus,
      )

      return jsonResponse(data)
    } catch (err) {
      context.log.error('Error fetching LOINC list:', err)
      return jsonResponse(
        'Error fetching LOINC list',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-update', {
  methods: ['POST'],
  route: 'loinc/update',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.updateOrganizationTestDetails(req)
    } catch (err) {
      context.log.error('Error updating organization test details:', err)
      return jsonResponse(
        'Error updating organization test details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-remove', {
  methods: ['POST'],
  route: 'loinc/remove',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.removeOrganizationTestDetails(req)
    } catch (err) {
      context.log.error('Error removing organization test details:', err)
      return jsonResponse(
        'Error removing organization test details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-update-status', {
  methods: ['GET'],
  route: 'loinc/update/status/{jobId}',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.getUpdateJobStatus(req)
    } catch (err) {
      context.log.error('Error getting update job status:', err)
      return jsonResponse(
        'Error getting update job status',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-remove-status', {
  methods: ['GET'],
  route: 'loinc/remove/status/{jobId}',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.getUpdateJobStatus(req)
    } catch (err) {
      context.log.error('Error getting remove job status:', err)
      return jsonResponse(
        'Error getting remove job status',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-import', {
  methods: ['POST'],
  route: 'loinc/import',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const file = req.body.file // Assuming file upload is handled
      const result = await labTestHandler.importLoincData(file)
      return jsonResponse(result)
    } catch (err) {
      context.log.error('Error importing LOINC data:', err)
      return jsonResponse(
        'Error importing LOINC data',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('loinc-tests-for-organization', {
  methods: ['GET'],
  route: 'loinc/tests-for-organization',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await labTestHandler.fetchLoincTestsForOrganization(req)
    } catch (err) {
      context.log.error('Error fetching LOINC tests for organization:', err)
      return jsonResponse(
        'Error fetching LOINC tests for organization',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
