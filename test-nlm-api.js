// Test script to verify NLM ICD API
const axios = require('axios')

const testQueries = [
  { query: 'MG26', type: 'code' },
  { query: 'fever', type: 'disease' },
  { query: 'diabetes', type: 'disease' },
  { query: 'A14', type: 'code' },
  { query: 'IB94.Z', type: 'code' }
]

async function testNlmApi(query, type) {
  try {
    console.log(`\n=== Testing NLM API - ${type}: "${query}" ===`)
    
    // Test NLM Clinical Tables API
    const response = await axios.get(
      'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
      {
        params: { 
          terms: query,
          df: 'code,title,definition,type,chapter',
          maxList: 10
        },
        timeout: 10000,
      }
    )
    
    console.log(`NLM API Response:`)
    console.log(`Total results: ${response.data[0]}`)
    console.log(`Codes: ${JSON.stringify(response.data[1])}`)
    console.log(`Display data:`)
    if (response.data[3]) {
      response.data[3].forEach((item, index) => {
        console.log(`  ${index + 1}. Code: ${item[0]}, Title: ${item[1]}, Type: ${item[2]}`)
      })
    }
    
  } catch (error) {
    console.log(`  Error with NLM API for ${query}: ${error.message}`)
  }
}

async function runTests() {
  console.log('Testing NLM Clinical Tables ICD-11 API...')
  
  for (const test of testQueries) {
    await testNlmApi(test.query, test.type)
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second between requests
  }
  
  console.log('\n=== Test Complete ===')
}

runTests()
