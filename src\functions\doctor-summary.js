const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const doctorHandler = require('../handlers/doctor-handler');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('doctor-summary', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'function',
    route: 'doctor/summary',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');
        const patientId = request.query.get("patientId");
        switch (request.method) {
            case HttpMethod.get:

                if (!patientId) {
                    return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest)
                }
                var res = await doctorHandler.getDoctorSummary(patientId);
                return jsonResponse(res);
            case HttpMethod.post:

                if (!patientId) {
                    return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest)
                }
                const payload = await request.json()
                if (!payload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)

                }
                payload.patientId = patientId;
                var data = await doctorHandler.createDoctorSummary(payload, decode.oid)
                return jsonResponse(data)
            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await doctorHandler.updateDoctorSummary(id, patchPayload, decode.oid)
                return jsonResponse(data);
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
