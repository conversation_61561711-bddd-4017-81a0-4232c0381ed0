const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class MedicineModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.productId = data.productId || ''
    this.productName = data.productName || ''
    this.marketerOrManufacturer = data.marketerOrManufacturer || ''
    this.saltComposition = data.saltComposition || ''
    this.medicineType = data.medicineType || ''
    this.introduction = data.introduction || ''
    this.benefits = data.benefits || ''
    this.description = data.description || ''
    this.howToUse = data.howToUse || ''
    this.safetyAdvise = data.safetyAdvise || ''
    this.ifMiss = data.ifMiss || ''
    this.packagingDetail = data.packagingDetail || ''
    this.package = data.package || ''
    this.qty = data.qty || ''
    this.productForm = data.productForm || ''
    this.mrp = data.mrp || ''
    this.prescriptionRequired = data.prescriptionRequired || ''
    this.factBox = data.factBox || ''
    this.primaryUse = data.primaryUse || ''
    this.storage = data.storage || ''
    this.useOf = data.useOf || ''
    this.commonSideEffect = data.commonSideEffect || ''
    this.alcoholInteraction = data.alcoholInteraction || ''
    this.pregnancyInteraction = data.pregnancyInteraction || ''
    this.lactationInteraction = data.lactationInteraction || ''
    this.drivingInteraction = data.drivingInteraction || ''
    this.kidneyInteraction = data.kidneyInteraction || ''
    this.liverInteraction = data.liverInteraction || ''
    this.manufacturerAddress = data.manufacturerAddress || ''
    this.countryOfOrigin = data.countryOfOrigin || ''
    this.qa = data.qa || ''
    this.howItWorks = data.howItWorks || ''
    this.interaction = data.interaction || ''
    this.manufacturerDetails = data.manufacturerDetails || ''
    this.marketerDetails = data.marketerDetails || ''
    this.expiration = data.expiration || ''
    this.reference = data.reference || ''
    this.imageUrl = data.imageUrl || ''
  }
}

module.exports = MedicineModel
