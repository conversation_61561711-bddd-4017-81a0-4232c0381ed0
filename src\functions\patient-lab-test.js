const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const labTestHandler = require('../handlers/patient-lab-test-handler')
const { jsonResponse } = require('../common/helper')

app.http('patient-lab-test', {
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  route: 'patient-lab-test',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      switch (req.method) {
        case HttpMethod.get:
          return await labTestHandler.getLabTests(req)
        case HttpMethod.post:
          return await labTestHandler.createLabTest(req)
        case HttpMethod.patch:
          return await labTestHandler.updateLabTest(req)
        case HttpMethod.delete:
          return await labTestHandler.deleteLabTest(req)
        default:
          return jsonResponse(`Unsupported HTTP method`, 405)
      }
    } catch (error) {
      context.log.error('Error handling patient-lab-test request:', error)
      return jsonResponse('Internal server error', 500)
    }
  },
})

app.http('patient-lab-test-details', {
  methods: ['GET'],
  route: 'patient-lab-test/details',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return labTestHandler.getLabTestDetails(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching labtest details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
app.http('patient-lab-test-search', {
  methods: ['POST'],
  route: 'patient-lab-test/search',
  authLevel: 'function',
  handler: (req, context) => labTestHandler.searchPatientLabTest(req),
})