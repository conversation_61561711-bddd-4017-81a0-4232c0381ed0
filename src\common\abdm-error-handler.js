/**
 * ABDM Error Handler
 * Provides comprehensive error handling and logging for ABDM operations
 */

const { logError, logInfo } = require('./logging')
const { HttpStatusCode } = require('axios')

class ABDMErrorHandler {
  /**
   * Handle ABDM API errors and provide standardized error responses
   * @param {Error} error - The error object
   * @param {string} operation - The operation that failed
   * @param {Object} context - Additional context information
   * @returns {Object} - Standardized error response
   */
  static handleError(error, operation, context = {}) {
    const errorInfo = {
      operation,
      timestamp: new Date().toISOString(),
      context,
      error: {
        message: error.message,
        name: error.name,
        stack: error.stack
      }
    }

    // Log the error with context
    logError(`ABDM ${operation} failed:`, errorInfo)

    // Handle different types of errors
    if (error.response) {
      // HTTP response error from ABDM API
      return this.handleHttpError(error, operation)
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      // Network connectivity errors
      return this.handleNetworkError(error, operation)
    } else if (error.code === 'ECONNABORTED') {
      // Timeout errors
      return this.handleTimeoutError(error, operation)
    } else {
      // Generic errors
      return this.handleGenericError(error, operation)
    }
  }

  /**
   * Handle HTTP response errors from ABDM API
   * @param {Error} error - The HTTP error
   * @param {string} operation - The operation that failed
   * @returns {Object} - Error response
   */
  static handleHttpError(error, operation) {
    const response = error.response
    const status = response.status
    const data = response.data

    logError(`ABDM HTTP Error - ${operation}:`, {
      status,
      statusText: response.statusText,
      data,
      headers: response.headers
    })

    // Map ABDM specific error codes to user-friendly messages
    const errorMessage = this.mapAbdmErrorMessage(data, status)

    return {
      success: false,
      error: errorMessage,
      details: {
        httpStatus: status,
        abdmError: data,
        operation
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Handle network connectivity errors
   * @param {Error} error - The network error
   * @param {string} operation - The operation that failed
   * @returns {Object} - Error response
   */
  static handleNetworkError(error, operation) {
    logError(`ABDM Network Error - ${operation}:`, error)

    return {
      success: false,
      error: 'Unable to connect to ABDM services. Please check your internet connection and try again.',
      details: {
        type: 'NETWORK_ERROR',
        code: error.code,
        operation
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Handle timeout errors
   * @param {Error} error - The timeout error
   * @param {string} operation - The operation that failed
   * @returns {Object} - Error response
   */
  static handleTimeoutError(error, operation) {
    logError(`ABDM Timeout Error - ${operation}:`, error)

    return {
      success: false,
      error: 'Request to ABDM services timed out. Please try again.',
      details: {
        type: 'TIMEOUT_ERROR',
        operation
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Handle generic errors
   * @param {Error} error - The generic error
   * @param {string} operation - The operation that failed
   * @returns {Object} - Error response
   */
  static handleGenericError(error, operation) {
    logError(`ABDM Generic Error - ${operation}:`, error)

    return {
      success: false,
      error: 'An unexpected error occurred while processing your request. Please try again.',
      details: {
        type: 'GENERIC_ERROR',
        message: error.message,
        operation
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Map ABDM specific error codes to user-friendly messages
   * @param {Object} abdmError - The ABDM error response
   * @param {number} httpStatus - The HTTP status code
   * @returns {string} - User-friendly error message
   */
  static mapAbdmErrorMessage(abdmError, httpStatus) {
    // Common ABDM error mappings
    const errorMappings = {
      // Authentication errors
      'INVALID_CLIENT_ID': 'Invalid ABDM client configuration. Please contact support.',
      'INVALID_CLIENT_SECRET': 'Invalid ABDM client configuration. Please contact support.',
      'SESSION_EXPIRED': 'ABDM session expired. Please try again.',
      'UNAUTHORIZED': 'Unauthorized access to ABDM services.',

      // Validation errors
      'INVALID_AADHAAR': 'Invalid Aadhaar number. Please check and try again.',
      'INVALID_MOBILE': 'Invalid mobile number. Please check and try again.',
      'INVALID_OTP': 'Invalid OTP. Please check and try again.',
      'OTP_EXPIRED': 'OTP has expired. Please request a new OTP.',
      'OTP_ATTEMPTS_EXCEEDED': 'Maximum OTP attempts exceeded. Please try again later.',

      // ABHA specific errors
      'ABHA_ALREADY_EXISTS': 'ABHA number already exists for this Aadhaar/Mobile.',
      'ABHA_NOT_FOUND': 'ABHA number not found.',
      'ABHA_INACTIVE': 'ABHA number is inactive.',
      'ABHA_BLOCKED': 'ABHA number is blocked.',

      // Transaction errors
      'INVALID_TRANSACTION_ID': 'Invalid transaction ID. Please start the process again.',
      'TRANSACTION_EXPIRED': 'Transaction has expired. Please start the process again.',
      'TRANSACTION_NOT_FOUND': 'Transaction not found. Please start the process again.',

      // Rate limiting
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please try again later.',
      'DAILY_LIMIT_EXCEEDED': 'Daily limit exceeded. Please try again tomorrow.',

      // Service errors
      'SERVICE_UNAVAILABLE': 'ABDM services are temporarily unavailable. Please try again later.',
      'MAINTENANCE_MODE': 'ABDM services are under maintenance. Please try again later.'
    }

    // Try to extract error code from ABDM response
    let errorCode = null
    if (abdmError) {
      errorCode = abdmError.code || abdmError.errorCode || abdmError.error_code
    }

    // Return mapped message or default based on HTTP status
    if (errorCode && errorMappings[errorCode]) {
      return errorMappings[errorCode]
    }

    // Default messages based on HTTP status
    switch (httpStatus) {
      case 400:
        return 'Invalid request. Please check your input and try again.'
      case 401:
        return 'Authentication failed. Please try again.'
      case 403:
        return 'Access denied. You do not have permission to perform this operation.'
      case 404:
        return 'Requested resource not found.'
      case 429:
        return 'Too many requests. Please try again later.'
      case 500:
        return 'ABDM server error. Please try again later.'
      case 502:
      case 503:
      case 504:
        return 'ABDM services are temporarily unavailable. Please try again later.'
      default:
        return abdmError?.message || 'An error occurred while processing your request.'
    }
  }

  /**
   * Log successful ABDM operations
   * @param {string} operation - The successful operation
   * @param {Object} context - Additional context information
   */
  static logSuccess(operation, context = {}) {
    logInfo(`ABDM ${operation} completed successfully`, {
      operation,
      timestamp: new Date().toISOString(),
      context
    })
  }

  /**
   * Log ABDM operation start
   * @param {string} operation - The operation being started
   * @param {Object} context - Additional context information
   */
  static logOperationStart(operation, context = {}) {
    logInfo(`ABDM ${operation} started`, {
      operation,
      timestamp: new Date().toISOString(),
      context
    })
  }

  /**
   * Validate and sanitize error response for client
   * @param {Object} errorResponse - The error response to sanitize
   * @returns {Object} - Sanitized error response
   */
  static sanitizeErrorResponse(errorResponse) {
    // Remove sensitive information from error response
    const sanitized = { ...errorResponse }
    
    if (sanitized.details) {
      // Remove stack traces and sensitive data
      delete sanitized.details.stack
      delete sanitized.details.headers
      
      // Remove sensitive ABDM error details
      if (sanitized.details.abdmError) {
        const { abdmError } = sanitized.details
        sanitized.details.abdmError = {
          message: abdmError.message,
          code: abdmError.code || abdmError.errorCode,
          // Only include safe fields
        }
      }
    }

    return sanitized
  }
}

module.exports = ABDMErrorHandler
