const { app } = require('@azure/functions')
const packageHandler = require('../handlers/prescription-package-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('get-prescription-packages', {
  methods: ['GET'],
  route: 'prescription-package',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    try {
      return await packageHandler.getPackagesByType(req)
    } catch (err) {
      console.error('Get prescription packages error:', err)
      return jsonResponse({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Error fetching prescription packages'
        }
      }, HttpStatusCode.InternalServerError)
    }
  },
})

app.http('create-prescription-package', {
  methods: ['POST'],
  route: 'prescription-package',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    try {
      return await packageHandler.createPackage(req)
    } catch (err) {
      console.error('Create prescription package error:', err)
      return jsonResponse({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Error creating prescription package'
        }
      }, HttpStatusCode.InternalServerError)
    }
  },
})

app.http('get-prescription-package-by-id', {
  methods: ['GET'],
  route: 'prescription-package/details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    try {
      return await packageHandler.getPackageById(req)
    } catch (err) {
      console.error('Get prescription package medicines error:', err)
      return jsonResponse({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Error fetching prescription in medicine package'
        }
      }, HttpStatusCode.InternalServerError)
    }
  },
})

app.http('update-prescription-package', {
  methods: ['PUT'],
  route: 'prescription-package',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    try {
      return await packageHandler.updatePackage(req)
    } catch (err) {
      console.error('Update prescription package error:', err)
      return jsonResponse({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Error updating prescription package'
        }
      }, HttpStatusCode.InternalServerError)
    }
  },
})

app.http('delete-prescription-package', {
  methods: ['DELETE'],
  route: 'prescription-package',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    try {
      return await packageHandler.deletePackage(req)
    } catch (err) {
      console.error('Delete prescription package error:', err)
      return jsonResponse({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Error deleting prescription package'
        }
      }, HttpStatusCode.InternalServerError)
    }
  },
})