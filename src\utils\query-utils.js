const buildDateFilterClause = (dateFilter) => {
  const now = new Date()
  let startDate = null
  let endDate = new Date(now)

  const setStartDate = (daysOffset, monthsOffset = 0) => {
    startDate = new Date(now)
    if (daysOffset) startDate.setDate(now.getDate() - daysOffset)
    if (monthsOffset) startDate.setMonth(now.getMonth() - monthsOffset)
    startDate.setUTCHours(0, 0, 0, 0)
  }

  const setEndDate = () => {
    endDate.setUTCHours(23, 59, 59, 999)
  }

  switch (dateFilter) {
    case 'last15Days':
      setStartDate(14)
      setEndDate()
      break

    case 'last30Days':
      setStartDate(29)
      setEndDate()
      break

    case 'past3Months':
      setStartDate(0, 3)
      setEndDate()
      break

    case 'thisYear':
      startDate = new Date(Date.UTC(now.getUTCFullYear(), 0, 1, 0, 0, 0, 0))
      setEndDate()
      break

    case 'today':
      setStartDate(0)
      setEndDate()
      break

    case 'ALL':
    case 'custom':
      return ''

    default:
      return ''
  }

  if (startDate) {
    const startDateISO =
      startDate.toISOString().split('T')[0] + 'T00:00:00.000Z'
    const endDateISO = endDate.toISOString().split('T')[0] + 'T23:59:59.999Z'
    return `AND c.updated_on >= "${startDateISO}" AND c.updated_on <= "${endDateISO}"`
  }

  return ''
}

module.exports = { buildDateFilterClause }
