const CosmosDbMetadata = require('./CosmosDb-Metadata-model')
class LabReportMetadataModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.fileName = data.fileName || ''
    this.fileSize = data.fileSize || 0
    this.fileType = data.fileType || ''
    this.blobPath = data.blobPath || ''

    this.encryptionKey = data.encryptionKey || ''
    this.iv = data.iv || ''

    this.patientId = data.patientId || ''
    this.labTestId = data.labTestId || ''

    this.uploadedAt = data.uploadedAt || new Date().toISOString()

    // OCR-related
    this.ocrStatus = data.ocrStatus || 'pending' // 'pending' | 'processing' | 'completed' | 'failed'
    this.detectedLanguage = data.detectedLanguage || null
    this.ocrData = data.ocrData || null
  }
}

module.exports = LabReportMetadataModel
