const openAIService = require('../services/openai-service')
const lifeStyleService = require('../services/lifestyle-service')
const logging = require('../common/logging')
const helper = require('../common/helper')

class LifestyleAmbientHandler {
  async identifySpeaker(transcript) {
    try {
      var classification =
        'you are an AI assistant help doctor to separate the chat transcript to know which one is doctor which one is patient, following the format:\n\n[{"speaker": "doctor", "message": ""},{"speaker": "patient", "message": ""}]\n\ncombine into 1 inline JSON without \'\\n\'\n The result must be in the format above strictly, if you don\'t see any information to analytic then just return the json format above with the null value'
      var result = await openAIService.chatCompletion(
        classification,
        transcript,
      )
      return result
    } catch (error) {
      logging.logError('Unable to identify Speaker', error)
      return null
    }
  }

  async processLifestyleQuestions(conversation, source) {
    try {
      logging.logInfo(`Processing lifestyle questions for source: ${source}`)

      const questionsData = await lifeStyleService.getLifeStyeBySourceName(
        source,
      )

      if (!questionsData || !Array.isArray(questionsData)) {
        logging.logError(`No questions found for source: ${source}`)
        return { questions: [] }
      }

      const processedQuestions = []

      for (const questionDoc of questionsData) {
        if (questionDoc.questions && Array.isArray(questionDoc.questions)) {
          for (const question of questionDoc.questions) {
            const processedQuestion = {
              id: question.id,
              title: question.title,
              icon: question.icon,
              fields: [],
            }

            if (question.fields && Array.isArray(question.fields)) {
              for (const field of question.fields) {
                if (field.label) {
                  const processedField = { ...field }

                  try {
                    // Handle table fields with special processing
                    if (field.type === 'table') {
                      processedField.value = await this.processTableField(
                        field,
                        conversation,
                      )
                    } else {
                      // Create a specialized prompt for extracting the value
                      const prompt = this.createValueExtractionPrompt(
                        field,
                        conversation,
                      )
                      const answer = await openAIService.chatCompletion(
                        prompt,
                        conversation,
                      )

                      processedField.value = this.processAnswerByFieldType(
                        field,
                        answer,
                      )
                    }
                  } catch (error) {
                    logging.logError(
                      `Error processing field ${field.label}:`,
                      error,
                    )
                    // Set default value based on field type
                    processedField.value =
                      this.getDefaultValueByFieldType(field)
                  }

                  processedQuestion.fields.push(processedField)
                }
              }
            }

            processedQuestions.push(processedQuestion)
          }
        }
      }

      return { questions: processedQuestions }
    } catch (error) {
      logging.logError('Unable to process lifestyle questions', error)
      return { questions: [] }
    }
  }

  createValueExtractionPrompt(field, conversation) {
    let prompt = `You are a medical AI assistant analyzing a conversation between a doctor and patient for lifestyle assessment.

From the following conversation, extract the answer for: "${field.label}"

Conversation: ${conversation}

Field Type: ${field.type}
`

    if (field.type === 'radio' && field.options) {
      prompt += `Available Options: ${field.options.join(', ')}

Instructions:
1. Find the patient's answer related to "${field.label}"
2. Match the answer to one of the available options: ${field.options.join(', ')}
3. Return ONLY the exact option that best matches the patient's response
4. If no clear match, return the closest option
5. If the patient mentions "Other" or something not in options and allowOtherSpecify is true, return "Other"
6. Do not include explanations, just return the option value

Answer:`
    } else if (field.type === 'slider') {
      prompt += `Slider Range: ${field.min} to ${field.max}
Description: ${field.description || ''}

Instructions:
1. Find the patient's numerical answer related to "${field.label}"
2. Extract the number mentioned by the patient
3. Ensure the number is between ${field.min} and ${field.max}
4. Return ONLY the number as an integer
5. If no clear number is mentioned, return ${Math.floor(
        (field.min + field.max) / 2,
      )}

Answer:`
    } else if (field.type === 'select' && field.options) {
      prompt += `Available Options: ${field.options.join(', ')}

Instructions:
1. Find the patient's answer related to "${field.label}"
2. Match the answer to one of the available options: ${field.options.join(', ')}
3. Return ONLY the exact option that best matches the patient's response
4. If no clear match, return the first option as default
5. Do not include explanations, just return the option value

Answer:`
    } else if (field.type === 'number') {
      prompt += `Instructions:
1. Find the patient's numerical answer related to "${field.label}"
2. Extract the number mentioned by the patient
3. Return ONLY the number (integer or decimal)
4. If no clear number is mentioned, return ""
5. Do not include units or explanations

Answer:`
    } else if (field.type === 'table') {
      prompt += `This is a table field with multiple rows of data.

Instructions:
1. Analyze the conversation for information related to "${field.label}"
2. Extract structured data that would fit in a table format
3. Look for multiple activities, items, or entries mentioned by the patient
4. Return "EXTRACT_TABLE_DATA" if table data is found, or "" if no relevant data
5. The actual table extraction will be handled separately

Answer:`
    } else if (field.type === 'conditional_select') {
      const dependsOnField = field.dependsOn
      prompt += `This is a conditional select field that depends on: ${dependsOnField}
Available Options: ${JSON.stringify(field.options)}

Instructions:
1. Find the patient's answer related to "${field.label}"
2. This field's options depend on another field's value
3. Return the most appropriate option based on the conversation
4. If unclear, return ""

Answer:`
    } else {
      prompt += `Instructions:
1. Find the patient's answer related to "${field.label}"
2. Return the exact answer or closest match
3. Keep the response concise and factual
4. If no answer is found, return ""

Answer:`
    }

    return prompt
  }

  processAnswerByFieldType(field, answer) {
    if (!answer || answer.trim() === '') {
      return this.getDefaultValueByFieldType(field)
    }

    const cleanAnswer = answer.toString().trim()

    if (field.type === 'radio' && field.options) {
      return this.processRadioAnswer(field, cleanAnswer)
    } else if (field.type === 'select' && field.options) {
      return this.processSelectAnswer(field, cleanAnswer)
    } else if (field.type === 'slider') {
      return this.processSliderAnswer(field, cleanAnswer)
    } else if (field.type === 'number') {
      return this.processNumberAnswer(cleanAnswer)
    } else if (field.type === 'table') {
      return this.processTableAnswer(field, cleanAnswer)
    } else if (field.type === 'conditional_select') {
      return this.processConditionalSelectAnswer(field, cleanAnswer)
    } else {
      return cleanAnswer
    }
  }

  processRadioAnswer(field, cleanAnswer) {
    const lowerAnswer = cleanAnswer.toLowerCase()

    // First, try exact match
    for (const option of field.options) {
      if (option.toLowerCase() === lowerAnswer) {
        return option
      }
    }

    // Then try partial match
    for (const option of field.options) {
      if (
        lowerAnswer.includes(option.toLowerCase()) ||
        option.toLowerCase().includes(lowerAnswer)
      ) {
        return option
      }
    }

    // If allowOtherSpecify and no match found, return "Other" if it exists
    if (field.allowOtherSpecify && field.options.includes('Other')) {
      return 'Other'
    }

    // Return first option as fallback
    return field.options[0]
  }

  processSelectAnswer(field, cleanAnswer) {
    const lowerAnswer = cleanAnswer.toLowerCase()

    // Try exact match first
    for (const option of field.options) {
      if (option.toLowerCase() === lowerAnswer) {
        return option
      }
    }

    // Then try partial match
    for (const option of field.options) {
      if (
        lowerAnswer.includes(option.toLowerCase()) ||
        option.toLowerCase().includes(lowerAnswer)
      ) {
        return option
      }
    }

    // Return first option as fallback
    return field.options[0]
  }

  processSliderAnswer(field, cleanAnswer) {
    const numberMatch = cleanAnswer.match(/\d+/)
    if (numberMatch) {
      let value = parseInt(numberMatch[0])
      // Ensure value is within slider range
      value = Math.max(field.min || 0, Math.min(field.max || 10, value))
      return value
    }

    // Return middle value as fallback
    return Math.floor(((field.min || 0) + (field.max || 10)) / 2)
  }

  processNumberAnswer(cleanAnswer) {
    // Extract number (integer or decimal)
    const numberMatch = cleanAnswer.match(/\d+\.?\d*/)
    if (numberMatch) {
      const value = parseFloat(numberMatch[0])
      return isNaN(value) ? '' : value
    }
    return ''
  }

  async processTableField(field, conversation) {
    try {
      // Create a specialized prompt for table data extraction
      const tablePrompt = this.createTableExtractionPrompt(field, conversation)
      const tableResponse = await openAIService.chatCompletion(
        tablePrompt,
        conversation,
      )

      // Try to parse the response as JSON
      try {
        const tableData = JSON.parse(tableResponse)
        if (Array.isArray(tableData)) {
          return tableData
        }
      } catch (parseError) {
        logging.logError('Failed to parse table data as JSON:', parseError)
      }

      // Return empty array if parsing fails
      return []
    } catch (error) {
      logging.logError('Error processing table field:', error)
      return []
    }
  }

  createTableExtractionPrompt(field, conversation) {
    const headers = field.headers || []
    const headerLabels = headers.map((h) => h.label).join(', ')

    return `You are a medical AI assistant analyzing a conversation for table data extraction.

From the following conversation, extract structured data for: "${field.label}"

Conversation: ${conversation}

Table Headers: ${headerLabels}

Instructions:
1. Look for multiple activities, items, or entries mentioned by the patient
2. Extract data that matches the table structure with headers: ${headerLabels}
3. Return the data as a JSON array of objects
4. Each object should have keys matching the header IDs: ${headers
      .map((h) => h.id)
      .join(', ')}
5. If no structured data is found, return an empty array: []
6. Example format: [{"activity_type": "Aerobics", "activity": "Walking", "duration": "30", "intensity": "Moderate", "frequency": "Daily"}]

Response (JSON only):`
  }

  processTableAnswer(field, cleanAnswer) {
    // For table fields, we need to check if the AI indicated table data was found
    if (cleanAnswer === 'EXTRACT_TABLE_DATA') {
      // Return empty array as placeholder - actual table extraction would need more complex logic
      return []
    }
    return []
  }

  processConditionalSelectAnswer(field, cleanAnswer) {
    // For conditional select, we need to determine which option set to use
    // This is complex as it depends on another field's value
    // For now, return the first available option from any category
    if (field.options && typeof field.options === 'object') {
      const allOptions = Object.values(field.options).flat()
      const lowerAnswer = cleanAnswer.toLowerCase()

      // Try to match against all possible options
      for (const option of allOptions) {
        if (
          option.toLowerCase() === lowerAnswer ||
          lowerAnswer.includes(option.toLowerCase()) ||
          option.toLowerCase().includes(lowerAnswer)
        ) {
          return option
        }
      }

      // Return first option from first category as fallback
      const firstCategory = Object.keys(field.options)[0]
      return field.options[firstCategory][0] || ''
    }
    return ''
  }

  getDefaultValueByFieldType(field) {
    if (field.type === 'radio' && field.options && field.options.length > 0) {
      return field.options[0]
    } else if (
      field.type === 'select' &&
      field.options &&
      field.options.length > 0
    ) {
      return field.options[0]
    } else if (field.type === 'slider') {
      return Math.floor(((field.min || 0) + (field.max || 10)) / 2)
    } else if (field.type === 'number') {
      return ''
    } else if (field.type === 'table') {
      return []
    } else if (field.type === 'conditional_select') {
      if (field.options && typeof field.options === 'object') {
        const firstCategory = Object.keys(field.options)[0]
        return field.options[firstCategory][0] || ''
      }
      return ''
    } else {
      return ''
    }
  }

  sanitizeFieldKey(label) {
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '')
      .trim()
  }

  async processLifestyleAmbientListening(transcript, source) {
    try {
      logging.logInfo(
        `Processing lifestyle ambient listening for source: ${source}`,
      )

      const conversation = await this.identifySpeaker(transcript)
      const objConversation = helper.parseJSON(conversation)

      if (!objConversation || !Array.isArray(objConversation)) {
        logging.logError('Failed to parse conversation')
        return {
          conversation: [],
          summary: {},
        }
      }

      const summary = await this.processLifestyleQuestions(conversation, source)

      return {
        conversation: objConversation,
        summary: summary,
      }
    } catch (error) {
      logging.logError('Unable to process lifestyle ambient listening', error)
      return {
        conversation: [],
        summary: {},
      }
    }
  }
}

module.exports = new LifestyleAmbientHandler()
