const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const testPackageHandler = require('../handlers/test-package-handler')
const { jsonResponse } = require('../common/helper')

app.http('test-package', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    switch (req.method) {
      case HttpMethod.get:
        try {
          return testPackageHandler.getTestPackagesByType(req)
        } catch (err) {
          return jsonResponse(
            'Error fetching test packages',
            HttpStatusCode.InternalServerError,
          )
        }

      case HttpMethod.post:
        try {
          return testPackageHandler.createTestPackage(req)
        } catch (err) {
          return jsonResponse(
            'Error creating test package',
            HttpStatusCode.InternalServerError,
          )
        }

      case HttpMethod.patch:
        try {
          return testPackageHandler.updateTestPackage(req)
        } catch (err) {
          return jsonResponse(
            'Error updating test package',
            HttpStatusCode.InternalServerError,
          )
        }

      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})

app.http('package-tests', {
  methods: ['GET'],
  route: 'package/tests',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return testPackageHandler.getTestsForPackage(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching tests for package',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('add-test-to-package', {
  methods: ['POST'],
  route: 'package/add-tests',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return testPackageHandler.addTestToPackage(req)
    } catch (err) {
      return jsonResponse(
        `Error adding tests to package`,
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('remove-test-from-package', {
  methods: ['POST'],
  route: 'package/remove-test',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return testPackageHandler.removeTestFromPackage(req)
    } catch (err) {
      return jsonResponse(
        `Error removing test from package`,
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('user-specific-packages', {
  methods: ['GET'],
  route: 'packages/user-specific',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return testPackageHandler.getUserSpecificPackages(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching user-specific test packages',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
