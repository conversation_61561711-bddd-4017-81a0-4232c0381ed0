const patientService = require('../services/patient-service');
const logging = require('../common/logging');
const { v4: uuidv4 } = require('uuid');

class PatientLifeStyleHandler {

    async getPatientLifeStyleBySourceName(patientId, source) {
        try {
            return await patientService.getPatientLifeStyleBySourceName(patientId, source);
        } catch (error) {
            logging.logError(`Unable to get patient lifestyle for patient ${patientId} and source ${source}`, error);
            return null;
        }
    }

    async createPatientLifeStyle(patientId, lifeStyle, create_by) {
        try {
            lifeStyle.create_by = create_by;
            lifeStyle.update_by = create_by;
            if (!lifeStyle.id) {
                lifeStyle.id = uuidv4();
            }
            return await patientService.createPatientLifeStyle(patientId, lifeStyle);
        } catch (error) {
            logging.logError(`Unable to create patient lifestyle for patient ${patientId}`, error);
            return null;
        }
    }

    async patchPatientLifeStyle(id, lifeStyle, update_by) {
        try {
            lifeStyle.update_by = update_by;
            return await patientService.patchPatientLifeStyle(id, lifeStyle);
        } catch (error) {
            logging.logError(`Unable to patch patient lifestyle for patient ${lifeStyle.patientId}`, error);
            return null;
        }
    }

    async getPatientLifeStyleBySourceNameAndSession(patientId, source, fromDate, toDate) {
        try {
            var data = await patientService.getPatientLifeStyleBySourceNameAndSession(patientId, source, fromDate, toDate);
            return data;
        } catch (error) {
            logging.logError(`Unable to get patient lifestyle for patient ${patientId} and source ${source}`, error);
            return null;
        }
    }
    
    async getPatientLifeStyleNotes(patientId) {
        try {
            var data = await patientService.getPatientLifeStyleNote(patientId);
            return data;
        } catch (error) {
            logging.logError(`Unable to get patient lifestyle notes for patient ${patientId}`, error);
            return null;
        }
    }

    async createPatientLifeStyleNotes(lifeStyleNote, created_by) {   
        try {
            lifeStyleNote.created_by = created_by;
            lifeStyleNote.updated_by = created_by;
            if (!lifeStyleNote.id) {
                lifeStyleNote.id = uuidv4();
            }
            return await patientService.createPatientLifeStyleNote(lifeStyleNote);
        } catch (error) {
            logging.logError(`Unable to create patient lifestyle notes for patient ${patientId}`, error);
            return null;
        }
    }

    async patchPatientLifeStyleNotes(id, lifeStyleNote, updated_by) {
        try {
            lifeStyleNote.updated_by = updated_by;
            return await patientService.patchPatientLifeStyleNote(id, lifeStyleNote);
        } catch (error) {
            logging.logError(`Unable to patch patient lifestyle notes for patient ${patientId}`, error);
            return null;
        }
    }
    async deletePatientLifeStyleNotes(id) {
        try {
            return await patientService.deletePatientLifeStyleNote(id);
        } catch (error) {
            logging.logError(`Unable to delete patient lifestyle notes for id ${id}`, error);
            return null;
        }
    }

}

module.exports = new PatientLifeStyleHandler();