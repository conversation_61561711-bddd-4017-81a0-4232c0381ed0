const getPackageById = (packageId) => {
  return `SELECT * FROM c WHERE c.id = "${packageId}"`;
};

const getPackageByName = (name) => {
  return `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}"`;
};

const getPackagesByType = (type) => {
  return `SELECT c.id, c.name, c.type FROM c WHERE c.type = "${type.trim()}" ORDER BY c.updated_on DESC`;
};

const getPackagesByTypeAndUser = (type, userId) => {
  return `SELECT c.id, c.name, c.type, c.createdBy, c.departmentName, c.description, c.isActive, c.created_on as createdAt, c.updated_on as updatedAt, ARRAY_LENGTH(c.medicines) as medicineCount FROM c WHERE c.type = "${type.trim()}" AND c.createdBy = "${userId}" AND c.isActive = true`;
};

const getPackageByNameAndUser = (name, userId, type) => {
  return `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}" AND c.createdBy = "${userId}" AND c.type = "${type}" AND c.isActive = true`;
};

const getPackageByNameAndType = (name, type) => {
  return `SELECT * FROM c WHERE LOWER(c.name) = "${name.trim().toLowerCase()}" AND c.type = "${type}" AND c.isActive = true`;
};

const getPackageWithMedicines = (packageId) => {
  return `SELECT c.id, c.name, c.type, c.createdBy, c.departmentName, c.description, c.isActive, c.created_on as createdAt, c.updated_on as updatedAt, c.medicines, c.usageStatistics FROM c WHERE c.id = "${packageId}"`;
};

module.exports = {
  getPackageById,
  getPackageByName,
  getPackagesByType,
  getPackagesByTypeAndUser,
  getPackageByNameAndUser,
  getPackageByNameAndType,
  getPackageWithMedicines,
};
