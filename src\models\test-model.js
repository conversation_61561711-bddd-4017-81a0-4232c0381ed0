const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class TestModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.LOINC_NUM = data.LOINC_NUM || ''
    this.COMPONENT = data.COMPONENT || ''
    this.PROPERTY = data.PROPERTY || ''
    this.TIME_ASPCT = data.TIME_ASPCT || ''
    this.SYSTEM = data.SYSTEM || ''
    this.SCALE_TYP = data.SCALE_TYP || ''
    this.METHOD_TYP = data.METHOD_TYP || ''
    this.CLASS = data.CLASS || ''
    this.VersionLastChanged = data.VersionLastChanged || ''
    this.CHNG_TYPE = data.CHNG_TYPE || ''
    this.DefinitionDescription = data.DefinitionDescription || ''
    this.STATUS = data.STATUS || ''
    this.CONSUMER_NAME = data.CONSUMER_NAME || ''
    this.CLASSTYPE = data.CLASSTYPE || ''
    this.FORMULA = data.FORMULA || ''
    this.EXMPL_ANSWERS = data.EXMPL_ANSWERS || ''
    this.SURVEY_QUEST_TEXT = data.SURVEY_QUEST_TEXT || ''
    this.SURVEY_QUEST_SRC = data.SURVEY_QUEST_SRC || ''
    this.UNITSREQUIRED = data.UNITSREQUIRED || ''
    this.RELATEDNAMES2 = data.RELATEDNAMES2 || ''
    this.SHORTNAME = data.SHORTNAME || ''
    this.ORDER_OBS = data.ORDER_OBS || ''
    this.HL7_FIELD_SUBFIELD_ID = data.HL7_FIELD_SUBFIELD_ID || ''
    this.EXTERNAL_COPYRIGHT_NOTICE = data.EXTERNAL_COPYRIGHT_NOTICE || ''
    this.EXAMPLE_UNITS = data.EXAMPLE_UNITS || ''
    this.LONG_COMMON_NAME = data.LONG_COMMON_NAME || ''
    this.EXAMPLE_UCUM_UNITS = data.EXAMPLE_UCUM_UNITS || ''
    this.STATUS_REASON = data.STATUS_REASON || ''
    this.STATUS_TEXT = data.STATUS_TEXT || ''
    this.CHANGE_REASON_PUBLIC = data.CHANGE_REASON_PUBLIC || ''
    this.COMMON_TEST_RANK = data.COMMON_TEST_RANK || ''
    this.COMMON_ORDER_RANK = data.COMMON_ORDER_RANK || ''
    this.HL7_ATTACHMENT_STRUCTURE = data.HL7_ATTACHMENT_STRUCTURE || ''
    this.EXTERNAL_COPYRIGHT_LINK = data.EXTERNAL_COPYRIGHT_LINK || ''
    this.PanelType = data.PanelType || ''
    this.AskAtOrderEntry = data.AskAtOrderEntry || ''
    this.AssociatedObservations = data.AssociatedObservations || ''
    this.VersionFirstReleased = data.VersionFirstReleased || ''
    this.ValidHL7AttachmentRequest = data.ValidHL7AttachmentRequest || ''
    this.DisplayName = data.DisplayName || ''
    this.id = data.id || data.LOINC_NUM || ''
  }
}

module.exports = TestModel
