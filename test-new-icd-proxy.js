// Test the new ICD proxy implementation
const axios = require('axios')

const testCases = [
  { query: 'E11', version: 'icd10', expected: 'Type 2 diabetes (ICD-10)' },
  { query: '5A11', version: 'icd11', expected: 'Type 2 diabetes (ICD-11)' },
  { query: 'MG26', version: 'icd11', expected: 'Fever (ICD-11)' },
  { query: 'R50.9', version: 'icd10', expected: 'Fever (ICD-10)' },
  { query: 'diabetes', version: 'both', expected: 'Disease search both versions' },
  { query: 'fever', version: 'both', expected: 'Disease search both versions' },
  { query: 'A15.0', version: 'icd10', expected: 'Tuberculosis (ICD-10)' }
]

async function testNewProxy() {
  console.log('Testing New ICD Proxy Implementation...\n')
  
  for (const test of testCases) {
    try {
      console.log(`=== Testing: ${test.expected} ===`)
      console.log(`Query: "${test.query}", Version: ${test.version}`)
      
      // Simulate the function call directly
      const { searchIcd10, searchIcd11 } = require('./src/functions/icd-proxy')
      
      let results = []
      
      if (test.version === 'icd10' || test.version === 'both') {
        console.log('Calling ICD-10 search...')
        const icd10Results = await searchIcd10(test.query, /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(test.query))
        results = results.concat(icd10Results)
        console.log(`ICD-10 results: ${icd10Results.length}`)
      }
      
      if (test.version === 'icd11' || test.version === 'both') {
        console.log('Calling ICD-11 search...')
        const icd11Results = await searchIcd11(test.query, /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(test.query))
        results = results.concat(icd11Results)
        console.log(`ICD-11 results: ${icd11Results.length}`)
      }
      
      console.log(`Total results: ${results.length}`)
      if (results.length > 0) {
        results.slice(0, 3).forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.version} - ${result.theCode}: ${result.title}`)
        })
      }
      
      console.log('✅ Test completed\n')
      
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}\n`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('=== All Tests Complete ===')
}

// Alternative: Test the actual HTTP endpoint if functions are running
async function testHttpEndpoint() {
  console.log('Testing HTTP Endpoint (if running)...\n')
  
  const baseUrl = 'http://localhost:7071/api/icd-proxy'
  
  for (const test of testCases) {
    try {
      console.log(`=== Testing HTTP: ${test.expected} ===`)
      
      const response = await axios.get(baseUrl, {
        params: {
          q: test.query,
          version: test.version
        },
        timeout: 10000
      })
      
      console.log(`Status: ${response.status}`)
      console.log(`Results: ${response.data.destinationEntities?.length || 0}`)
      
      if (response.data.destinationEntities && response.data.destinationEntities.length > 0) {
        response.data.destinationEntities.slice(0, 3).forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.version} - ${result.theCode}: ${result.title}`)
        })
      }
      
      console.log('✅ HTTP Test completed\n')
      
    } catch (error) {
      console.log(`❌ HTTP Test failed: ${error.message}\n`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

// Run tests
console.log('Choose test method:')
console.log('1. Direct function test (requires importing functions)')
console.log('2. HTTP endpoint test (requires running Azure Functions)')
console.log('\nRunning HTTP endpoint test...\n')

testHttpEndpoint()
