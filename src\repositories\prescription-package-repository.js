const {
  getPackageById,
  getPackageByName,
  getPackagesByType,
  getPackagesByTypeAndUser,
  getPackageByNameAndUser,
  getPackageByNameAndType,
  getPackageWithMedicines,
} = require('../queries/package-query')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const packageContainer = 'packages'

class PackageRepository {
  async getPackageById(packageId) {
    const query = getPackageById(packageId)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async getPackageByName(name) {
    const query = getPackageByName(name)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async createPackage(packageData) {
    return cosmosDbContext.createItem(packageData, packageContainer)
  }

  async upsertPackage(packageData) {
    return cosmosDbContext.upsertItem(
      packageData.id,
      packageData,
      packageContainer,
    )
  }

  async getPackagesByType(type) {
    const query = getPackagesByType(type)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async getPackagesByTypeAndUser(type, userId) {
    const query = getPackagesByTypeAndUser(type, userId)
    return cosmosDbContext.queryItems(query, packageContainer)
  }
  
  async getPackageByNameAndUser(name, userId, type) {
    const query = getPackageByNameAndUser(name, userId, type)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async getPackageByNameAndType(name, type) {
    const query = getPackageByNameAndType(name, type)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async getPackageWithMedicines(packageId) {
    const query = getPackageWithMedicines(packageId)
    return cosmosDbContext.queryItems(query, packageContainer)
  }

  async deletePackage(packageId) {
    return cosmosDbContext.deleteItem(packageId, packageId, packageContainer)
  }
}

module.exports = new PackageRepository()
