const axios = require('axios')
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')

    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      console.log(`ICD Search - Query: "${query}"`)

      // Simple approach: Just call the search API and return raw results
      const response = await axios.get(
        'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
        {
          params: { q: query },
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'API-Version': 'v2',
            'Accept-Language': 'en',
          },
          timeout: 10000,
        }
      )

      console.log(`API Response: Found ${response.data?.destinationEntities?.length || 0} entities`)

      if (response.data && response.data.destinationEntities) {
        // Clean up HTML tags in titles but keep original structure
        const cleanedData = {
          ...response.data,
          destinationEntities: response.data.destinationEntities.map(entity => ({
            ...entity,
            title: cleanTitle(entity.title)
          }))
        }

        return jsonResponse(cleanedData)
      }

      // Return empty result in same format as API
      return jsonResponse({
        error: false,
        errorMessage: null,
        resultChopped: false,
        wordSuggestionsChopped: false,
        guessType: 0,
        uniqueSearchId: "",
        words: null,
        destinationEntities: []
      })

    } catch (error) {
      console.error('Error in ICD search:', error.message)
      if (error.response) {
        console.error('API Error Status:', error.response.status)
        console.error('API Error Data:', error.response.data)
      }

      return jsonResponse({
        error: 'Error fetching ICD data',
        details: error.message,
        query: query
      }, 500)
    }
  },
})

// Clean HTML tags from title
function cleanTitle(title) {
  if (!title) return 'Unknown'
  // Remove HTML tags like <em class='found'>fever</em>
  return title.replace(/<[^>]*>/g, '')
}

// Search by ICD code
async function searchByIcdCode(code) {
  try {
    console.log(`Searching for ICD code: ${code}`)

    // First try to search and filter by exact theCode match
    const searchResponse = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: code },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    console.log(`Search response for code ${code}: Found ${searchResponse.data?.destinationEntities?.length || 0} entities`)

    if (searchResponse.data && searchResponse.data.destinationEntities) {
      // Log all theCode values for debugging
      const allCodes = searchResponse.data.destinationEntities.map(entity => entity.theCode).filter(Boolean)
      console.log(`All theCode values found:`, allCodes)

      // Filter results to find exact theCode matches
      const exactMatches = searchResponse.data.destinationEntities.filter(entity =>
        entity.theCode && entity.theCode.toUpperCase() === code.toUpperCase()
      )

      if (exactMatches.length > 0) {
        console.log(`Found ${exactMatches.length} exact matches for code ${code}`)
        return formatIcdSearchResults({ destinationEntities: exactMatches })
      }

      // If no exact matches, look for partial matches
      const partialMatches = searchResponse.data.destinationEntities.filter(entity =>
        entity.theCode && entity.theCode.toUpperCase().includes(code.toUpperCase())
      )

      if (partialMatches.length > 0) {
        console.log(`Found ${partialMatches.length} partial matches for code ${code}`)
        return formatIcdSearchResults({ destinationEntities: partialMatches })
      }

      // If still no matches, return all results (maybe the code format is different)
      console.log(`No exact/partial matches found for code ${code}, returning all ${searchResponse.data.destinationEntities.length} results`)
      return formatIcdSearchResults(searchResponse.data)
    }

    return []
  } catch (error) {
    console.error('ICD code search failed:', error.message)
    return []
  }
}

// Search by disease name
async function searchByDiseaseName(diseaseName) {
  try {
    const response = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: diseaseName },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    // Return raw API response since it's already in good format
    if (response.data && response.data.destinationEntities) {
      return response.data.destinationEntities.map(entity => ({
        icdCode: entity.theCode || '',
        title: cleanTitle(entity.title || ''),
        description: entity.definition || '',
        source: 'ICD-11',
        url: entity.id || '',
        type: 'search_result',
        score: entity.score || 0,
        chapter: entity.chapter || '',
        isLeaf: entity.isLeaf || false
      }))
    }

    return []
  } catch (error) {
    console.error('Disease name search failed:', error.message)
    return []
  }
}



// Format ICD search results
function formatIcdSearchResults(data) {
  if (!data || !data.destinationEntities) {
    return []
  }

  return data.destinationEntities.map(entity => ({
    icdCode: entity.theCode || entity.code || '',
    title: cleanTitle(entity.title || entity.name || 'Unknown'),
    description: entity.definition || entity.description || '',
    source: 'ICD-11',
    url: entity.id || entity.url || '',
    type: 'search_result',
    score: entity.score || 0,
    chapter: entity.chapter || '',
    isLeaf: entity.isLeaf || false
  })).filter(result => result.icdCode) // Only return results with valid ICD codes
}

// Clean HTML tags from title
function cleanTitle(title) {
  if (!title) return 'Unknown'
  // Remove HTML tags like <em class='found'>diabetes</em>
  return title.replace(/<[^>]*>/g, '')
}
