const axios = require('axios')
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const searchType = request.query.get('type') || 'auto'

    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      console.log(`ICD Search - Query: "${query}", Type: ${searchType}`)

      // Determine if the query looks like an ICD code
      const isIcdCode = /^[A-Z]{1,2}\d+(\.[A-Z0-9]+)?$/i.test(query.trim())
      console.log(`Detected as ICD code: ${isIcdCode}`)

      // Use NLM Clinical Tables API (supports both code and disease name searches)
      const response = await axios.get(
        'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
        {
          params: {
            terms: query,
            df: 'code,title,definition,type,chapter,entityId',
            maxList: 50
          },
          timeout: 10000,
        }
      )

      console.log(`NLM API Response: Found ${response.data[0]} total results, returning ${response.data[1]?.length || 0}`)

      if (response.data && response.data[1] && response.data[1].length > 0) {
        // Convert NLM API format to your expected format
        const destinationEntities = response.data[3].map((item, index) => ({
          id: response.data[2] && response.data[2][index] ? response.data[2][index].entityId : '',
          title: item[1] || 'Unknown',
          theCode: item[0] || '',
          definition: item[2] || '',
          type: item[3] || '',
          chapter: item[4] || '',
          entityId: response.data[2] && response.data[2][index] ? response.data[2][index].entityId : '',
          score: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 1.0 : 0.8,
          isLeaf: true,
          source: 'ICD-11 (NLM)',
          matchType: isIcdCode && item[0].toUpperCase() === query.toUpperCase() ? 'exact_code' : 'search_result'
        }))

        // If searching by code, prioritize exact matches
        if (isIcdCode) {
          const exactMatches = destinationEntities.filter(entity =>
            entity.theCode.toUpperCase() === query.toUpperCase()
          )

          if (exactMatches.length > 0) {
            console.log(`Found ${exactMatches.length} exact code matches`)
            return jsonResponse({
              error: false,
              errorMessage: null,
              resultChopped: false,
              wordSuggestionsChopped: false,
              guessType: 1,
              uniqueSearchId: "",
              words: null,
              destinationEntities: exactMatches
            })
          }
        }

        // Return all results
        return jsonResponse({
          error: false,
          errorMessage: null,
          resultChopped: false,
          wordSuggestionsChopped: false,
          guessType: 2,
          uniqueSearchId: "",
          words: null,
          destinationEntities: destinationEntities
        })
      }

      // No results found
      return jsonResponse({
        error: false,
        errorMessage: isIcdCode
          ? `ICD code "${query}" not found in the database.`
          : `No results found for "${query}". Try a different search term.`,
        resultChopped: false,
        wordSuggestionsChopped: false,
        guessType: 0,
        uniqueSearchId: "",
        words: null,
        destinationEntities: []
      })

    } catch (error) {
      console.error('Error in NLM ICD search:', error.message)
      if (error.response) {
        console.error('API Error Status:', error.response.status)
        console.error('API Error Data:', error.response.data)
      }

      return jsonResponse({
        error: 'Error fetching ICD data from NLM API',
        details: error.message,
        query: query
      }, 500)
    }
  },
})
