const axios = require('axios')
const { app, HttpResponse } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const searchType = request.query.get('type') || 'auto' // auto, disease, code

    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      // Determine if the query looks like an ICD code (starts with letter followed by numbers)
      const isIcdCode = /^[A-Z]\d+/.test(query.toUpperCase())

      let searchResults = []

      // Try different search strategies based on query type
      if (searchType === 'code' || (searchType === 'auto' && isIcdCode)) {
        // Search by ICD code
        searchResults = await searchByIcdCode(query)
      }

      if (searchResults.length === 0 && (searchType === 'disease' || searchType === 'auto')) {
        // Search by disease name
        searchResults = await searchByDiseaseName(query)
      }

      return jsonResponse({
        query: query,
        searchType: isIcdCode ? 'code' : 'disease',
        results: searchResults,
        total: searchResults.length
      })

    } catch (error) {
      console.error('Error in ICD search:', error)
      return jsonResponse(
        { error: 'Error fetching ICD data', details: error.message },
        500
      )
    }
  },
})

// Search by ICD code
async function searchByIcdCode(code) {
  try {
    // Try exact code lookup first
    const exactResponse = await axios.get(
      `https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/codeinfo/${code.toUpperCase()}`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    if (exactResponse.data) {
      return [formatIcdResult(exactResponse.data, code)]
    }
  } catch (error) {
    console.log(`Exact code lookup failed for ${code}, trying search...`)
  }

  // If exact lookup fails, try search
  try {
    const searchResponse = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: code },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    return formatIcdSearchResults(searchResponse.data)
  } catch (error) {
    console.error('ICD code search failed:', error.message)
    return []
  }
}

// Search by disease name
async function searchByDiseaseName(diseaseName) {
  try {
    const response = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: diseaseName },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    return formatIcdSearchResults(response.data)
  } catch (error) {
    console.error('Disease name search failed:', error.message)
    return []
  }
}

// Format single ICD result
function formatIcdResult(data, code) {
  return {
    icdCode: code,
    title: data.title || data.name || 'Unknown',
    description: data.definition || data.description || '',
    source: 'ICD-11',
    url: data.url || '',
    type: 'exact_match'
  }
}

// Format ICD search results
function formatIcdSearchResults(data) {
  if (!data || !data.destinationEntities) {
    return []
  }

  return data.destinationEntities.map(entity => ({
    icdCode: extractIcdCode(entity.theCode || entity.code || ''),
    title: entity.title || entity.name || 'Unknown',
    description: entity.definition || entity.description || '',
    source: 'ICD-11',
    url: entity.url || '',
    type: 'search_result',
    score: entity.score || 0
  })).filter(result => result.icdCode) // Only return results with valid ICD codes
}

// Extract ICD code from various formats
function extractIcdCode(codeString) {
  if (!codeString) return ''

  // Extract code from URL or string like "http://id.who.int/icd/entity/123456789" or "A00.0"
  const matches = codeString.match(/([A-Z]\d+(?:\.\d+)?)/i)
  return matches ? matches[1].toUpperCase() : ''
}
