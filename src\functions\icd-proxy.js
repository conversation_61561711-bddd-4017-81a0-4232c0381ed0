const axios = require('axios')
const { app, HttpResponse } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')
    if (!query) {
      return new HttpResponse('Missing "q" parameter', { status: 400 })
    }

    try {
      const response = await axios.get(
        'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
        {
          params: { q: query },
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'API-Version': 'v2',
            'Accept-Language': 'en',
            // 'Ocp-Apim-Subscription-Key': 'ac5d8ae93c0f4f9892390f9c9646e749',
          },
          timeout: 10000,
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
          transitional: {
            forcedJSONParsing: true,
            clarifyTimeoutError: true,
          },
        },
      )
      return jsonResponse(response.data)
    } catch (error) {
      console.error('Error in Axios request:', error)
      if (error.response) {
        console.error('Status:', error.response.status)
        console.error('Response data:', error.response.data)
      }

      return jsonResponse(
        'Error fetching data',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
