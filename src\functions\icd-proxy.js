const axios = require('axios')
const { app, HttpResponse } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('icd-proxy', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'icd-proxy',
  handler: async (request, context) => {
    const query = request.query.get('q')
    const searchType = request.query.get('type') || 'auto' // auto, disease, code

    if (!query) {
      return jsonResponse({ error: 'Missing "q" parameter' }, 400)
    }

    try {
      // Determine if the query looks like an ICD code
      // ICD codes can be: A00, A00.1, PB28, 8B92.2, IB94.Z, etc.
      const isIcdCode = /^[A-Z0-9]+[0-9]*(\.[A-Z0-9]+)?$/i.test(query.trim())

      let searchResults = []

      // Log detection result
      console.log(`Query: "${query}", Detected as ICD code: ${isIcdCode}, Search type: ${searchType}`)

      // Try different search strategies based on query type
      if (searchType === 'code' || (searchType === 'auto' && isIcdCode)) {
        // Search by ICD code
        console.log(`Searching by ICD code: ${query}`)
        searchResults = await searchByIcdCode(query)
        console.log(`ICD code search returned ${searchResults.length} results`)
      }

      if (searchResults.length === 0 && (searchType === 'disease' || searchType === 'auto')) {
        // Search by disease name
        console.log(`Searching by disease name: ${query}`)
        searchResults = await searchByDiseaseName(query)
        console.log(`Disease name search returned ${searchResults.length} results`)
      }

      return jsonResponse({
        query: query,
        searchType: isIcdCode ? 'code' : 'disease',
        detectedAsCode: isIcdCode,
        actualSearchPerformed: searchResults.length > 0 ? (isIcdCode ? 'code' : 'disease') : 'both_failed',
        results: searchResults,
        total: searchResults.length
      })

    } catch (error) {
      console.error('Error in ICD search:', error)
      return jsonResponse(
        { error: 'Error fetching ICD data', details: error.message },
        500
      )
    }
  },
})

// Search by ICD code
async function searchByIcdCode(code) {
  try {
    console.log(`Searching for ICD code: ${code}`)

    // First try to search and filter by exact theCode match
    const searchResponse = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: code },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    console.log(`Search response for code ${code}: Found ${searchResponse.data?.destinationEntities?.length || 0} entities`)

    if (searchResponse.data && searchResponse.data.destinationEntities) {
      // Log all theCode values for debugging
      const allCodes = searchResponse.data.destinationEntities.map(entity => entity.theCode).filter(Boolean)
      console.log(`All theCode values found:`, allCodes)

      // Filter results to find exact theCode matches
      const exactMatches = searchResponse.data.destinationEntities.filter(entity =>
        entity.theCode && entity.theCode.toUpperCase() === code.toUpperCase()
      )

      if (exactMatches.length > 0) {
        console.log(`Found ${exactMatches.length} exact matches for code ${code}`)
        return formatIcdSearchResults({ destinationEntities: exactMatches })
      }

      // If no exact matches, look for partial matches
      const partialMatches = searchResponse.data.destinationEntities.filter(entity =>
        entity.theCode && entity.theCode.toUpperCase().includes(code.toUpperCase())
      )

      if (partialMatches.length > 0) {
        console.log(`Found ${partialMatches.length} partial matches for code ${code}`)
        return formatIcdSearchResults({ destinationEntities: partialMatches })
      }

      // If still no matches, return all results (maybe the code format is different)
      console.log(`No exact/partial matches found for code ${code}, returning all ${searchResponse.data.destinationEntities.length} results`)
      return formatIcdSearchResults(searchResponse.data)
    }

    return []
  } catch (error) {
    console.error('ICD code search failed:', error.message)
    return []
  }
}

// Search by disease name
async function searchByDiseaseName(diseaseName) {
  try {
    const response = await axios.get(
      'https://icd-api.salmonsmoke-3ede2fe8.centralindia.azurecontainerapps.io/icd/release/11/2024-01/mms/search',
      {
        params: { q: diseaseName },
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'API-Version': 'v2',
          'Accept-Language': 'en',
        },
        timeout: 10000,
      }
    )

    return formatIcdSearchResults(response.data)
  } catch (error) {
    console.error('Disease name search failed:', error.message)
    return []
  }
}



// Format ICD search results
function formatIcdSearchResults(data) {
  if (!data || !data.destinationEntities) {
    return []
  }

  return data.destinationEntities.map(entity => ({
    icdCode: entity.theCode || entity.code || '',
    title: cleanTitle(entity.title || entity.name || 'Unknown'),
    description: entity.definition || entity.description || '',
    source: 'ICD-11',
    url: entity.id || entity.url || '',
    type: 'search_result',
    score: entity.score || 0,
    chapter: entity.chapter || '',
    isLeaf: entity.isLeaf || false
  })).filter(result => result.icdCode) // Only return results with valid ICD codes
}

// Clean HTML tags from title
function cleanTitle(title) {
  if (!title) return 'Unknown'
  // Remove HTML tags like <em class='found'>diabetes</em>
  return title.replace(/<[^>]*>/g, '')
}
