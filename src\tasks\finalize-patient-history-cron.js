const cron = require('node-cron')
const patientService = require('../services/patient-service')
const { RecordStatus } = require('../common/constant')

const finalizePatientRecordsCron = () => {
  cron.schedule('0 * * * *', async () => {
    console.log('[Cron] Patient records finalization job running...')

    try {
      const cutoffDate = new Date(
        Date.now() - 24 * 60 * 60 * 1000,
      ).toISOString()

      const query = `SELECT * FROM c WHERE c.status != 'finalized' AND c.created_on < '${cutoffDate}'`

      await finalizePatientHistory(query)
      await finalizePatientLifestyle(query)

      console.log('[Cron] Patient records finalization job completed')
    } catch (error) {
      console.error('[Cron] Finalization job failed:', error)
    }
  })
}

async function finalizePatientHistory(query) {
  try {
    const records = await patientService.getPatientHistoryByQuey(query)

    if (!records?.length) {
      console.log('No history records to finalize')
      return
    }

    for (const record of records) {
      await patientService.updatePatientHistory({
        id: record.id,
        ...record,
        status: RecordStatus.FINALIZED,
        updated_on: new Date().toISOString(),
      })
      console.log(`Finalized history record ${record.id}`)
    }
  } catch (error) {
    console.error('History finalization failed:', error)
  }
}

async function finalizePatientLifestyle(query) {
  try {
    const records = await patientService.getPatientLifeStyleByQuery(query)

    if (!records?.length) {
      console.log('No lifestyle records to finalize')
      return
    }

    console.log(`Found ${records.length} lifestyle records to finalize`)

    for (const record of records) {
      await patientService.patchPatientLifeStyle(record.id, {
        status: RecordStatus.FINALIZED,
        updated_on: new Date().toISOString(),
      })
      console.log(`Finalized lifestyle record ${record.id}`)
    }
  } catch (error) {
    console.error('Lifestyle finalization failed:', error)
  }
}

module.exports = finalizePatientRecordsCron
