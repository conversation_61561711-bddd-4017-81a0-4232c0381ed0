const organizationService = require('../../services/admin/organization-service')
const { jsonResponse } = require('../../common/helper')
const { HttpStatusCode } = require('axios')

class OrganizationHandler {
  async createOrganization(req) {
    try {
      const body = await req.json()

      // Validate organization data
      const validationError = await this.validateOrganizationData(body)
      if (validationError) {
        return jsonResponse(validationError, 400)
      }

      const result = await organizationService.createOrganization(body)
      return jsonResponse(result, 201)
    } catch (err) {
      return jsonResponse('Error creating organization', 500)
    }
  }

  async editOrganization(req) {
    try {
      const body = await req.json()

      const validationError = await this.validateOrganizationData(body, body.id)
      if (validationError) {
        return jsonResponse(validationError, 400)
      }

      if (body.isActive === false) {
        await organizationService.deactivateOrganizationUsers(body.id)
      }

      const result = await organizationService.editOrganization(body)
      return jsonResponse(result, 200)
    } catch (err) {
      return jsonResponse('Error editing organization', 500)
    }
  }

  async deleteOrganization(req) {
    try {
      const organizationId = req.query.get('organizationId')
      if (!organizationId) {
        return jsonResponse(
          'Organization ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const isLinkedToUsers =
        await organizationService.checkOrganizationLinkedToUsers(organizationId)
      if (isLinkedToUsers) {
        return jsonResponse(
          'Cannot delete organization since users are linked',
          HttpStatusCode.BadRequest,
        )
      }

      await organizationService.deleteOrganization(organizationId)
      return jsonResponse(
        `Organization with ID ${organizationId} has been successfully deleted.`,
        HttpStatusCode.Ok,
      )
    } catch (err) {
      return jsonResponse(
        `Error deleting organization with ID ${req.query.get(
          'organizationId',
        )}: ${err.message}`,
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async validateOrganizationData(data, excludeId = null) {
    const normalizedExcludeId = excludeId ? String(excludeId).trim() : null

    const existingOrganizations = await organizationService.listOrganizations(
      '',
      1000,
      1,
    )

    const isNameDuplicate = existingOrganizations.organizations.some((org) => {
      const orgId = String(org.id).trim()
      const isDuplicate =
        org.name === data.name && orgId !== normalizedExcludeId
      return isDuplicate
    })

    const isEmailDuplicate = existingOrganizations.organizations.some((org) => {
      const orgId = String(org.id).trim()
      const isDuplicate =
        org.contactEmail === data.contactEmail && orgId !== normalizedExcludeId
      return isDuplicate
    })

    if (isNameDuplicate) {
      return 'Organization name must be unique'
    }
    if (isEmailDuplicate) {
      return 'Contact email must be unique'
    }
    return null
  }

  async listOrganizations(req) {
    try {
      const nameFilter = req.query.get('name') || ''
      const pageSize = parseInt(req.query.get('pageSize'), 10) || 10
      const pageNumber = parseInt(req.query.get('page'), 10) || 1

      const result = await organizationService.listOrganizations(
        nameFilter,
        pageSize,
        pageNumber,
      )

      return jsonResponse(
        {
          records: result.organizations,
          totalRecords: result.totalCount,
          totalPages: result.totalPages,
          currentPage: result.currentPage,
        },
        200,
      )
    } catch (err) {
      console.error('Error in listOrganizations:', err)
      return jsonResponse('Error fetching organizations', 500)
    }
  }

  async getOrganizationById(req) {
    try {
      const id = req.query.get('organizationId')
      if (!id) {
        return jsonResponse(
          'Organization ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      const organization = await organizationService.getOrganizationById(id)
      if (!organization) {
        return jsonResponse('Organization not found', HttpStatusCode.NotFound)
      }

      return jsonResponse(organization, HttpStatusCode.Ok)
    } catch (err) {
      return jsonResponse(
        'Error fetching organization details',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new OrganizationHandler()
