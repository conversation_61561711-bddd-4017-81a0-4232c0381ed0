const { app } = require('@azure/functions');
const patientLifeStyleHandler = require('../handlers/patient-lifestyle-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('patient-lifestyle-note', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'function',
    route: 'patient/lifestyle/note',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);

        const decode = context.extraInputs.get('decode');
        const patientId = request.query.get("patientId");

        switch (request.method) {
            case HttpMethod.get:
                if (!patientId) {
                    return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest)
                }
                var res = await patientLifeStyleHandler.getPatientLifeStyleNotes(patientId);
                return jsonResponse(res);
            case HttpMethod.post:
                const payload = await request.json()
                if (!payload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                if (!payload.patientId) {
                    return jsonResponse(`Missing patientId in the payload`, HttpStatusCode.BadRequest)
                }
                var data = await patientLifeStyleHandler.createPatientLifeStyleNotes(payload, decode.oid);
                return jsonResponse(data)
            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await patientLifeStyleHandler.patchPatientLifeStyleNotes(id, patchPayload, decode.oid);
                return jsonResponse(data);

            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }

    }
});
