// Test script to verify both ICD-10 and ICD-11 APIs
const axios = require('axios')

const testQueries = [
  { query: 'E11', type: 'code', description: 'Type 2 diabetes (ICD-10)' },
  { query: '5A11', type: 'code', description: 'Type 2 diabetes (ICD-11)' },
  { query: 'MG26', type: 'code', description: 'Fever (ICD-11)' },
  { query: 'R50', type: 'code', description: 'Fever (ICD-10)' },
  { query: 'diabetes', type: 'disease', description: 'Disease search' },
  { query: 'fever', type: 'disease', description: 'Disease search' },
  { query: 'A15.0', type: 'code', description: 'Tuberculosis (ICD-10)' }
]

async function testIcd10Api(query, description) {
  try {
    console.log(`\n=== Testing ICD-10 API - ${description}: "${query}" ===`)
    
    const response = await axios.get(
      'https://clinicaltables.nlm.nih.gov/api/icd10cm/v3/search',
      {
        params: { 
          terms: query,
          sf: 'code,name',
          df: 'code,name',
          maxList: 10
        },
        timeout: 10000,
      }
    )
    
    console.log(`ICD-10 Results: ${response.data[0]} total, showing ${response.data[1]?.length || 0}`)
    if (response.data[3]) {
      response.data[3].forEach((item, index) => {
        console.log(`  ${index + 1}. Code: ${item[0]}, Name: ${item[1]}`)
      })
    }
    
    return response.data
    
  } catch (error) {
    console.log(`  Error with ICD-10 API for ${query}: ${error.message}`)
    return null
  }
}

async function testIcd11Api(query, description) {
  try {
    console.log(`\n=== Testing ICD-11 API - ${description}: "${query}" ===`)
    
    const response = await axios.get(
      'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
      {
        params: { 
          terms: query,
          df: 'code,title,definition',
          maxList: 10
        },
        timeout: 10000,
      }
    )
    
    console.log(`ICD-11 Results: ${response.data[0]} total, showing ${response.data[1]?.length || 0}`)
    if (response.data[3]) {
      response.data[3].forEach((item, index) => {
        console.log(`  ${index + 1}. Code: ${item[0]}, Title: ${item[1]}`)
      })
    }
    
    return response.data
    
  } catch (error) {
    console.log(`  Error with ICD-11 API for ${query}: ${error.message}`)
    return null
  }
}

async function runTests() {
  console.log('Testing Both ICD-10 and ICD-11 APIs...')
  
  for (const test of testQueries) {
    // Test both APIs for each query
    await testIcd10Api(test.query, test.description)
    await testIcd11Api(test.query, test.description)
    
    console.log('\n' + '='.repeat(80))
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second between requests
  }
  
  console.log('\n=== Test Complete ===')
}

runTests()
