const { app } = require('@azure/functions');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const { HttpMethod } = require('../common/constant');
const patientHandler = require('../handlers/patient-handler');

app.http('patient-consultation', {
    methods: ['GET', 'POST', 'PUT'],
    authLevel: 'anonymous',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);

        const decode = context.extraInputs.get('decode');
        const patientId = req.query.get('patientId')
        if (!patientId) {
            return jsonResponse(`Missing PatientId`, HttpStatusCode.BadRequest);
        }

        switch (req.method) {
            case HttpMethod.get:
                var data = await patientHandler.GetPatientConsultations(patientId);
                return jsonResponse(data);

            case HttpMethod.post:
            case HttpMethod.put:
                if (!req.body) {
                    return jsonResponse(`Missing request payload`, HttpStatusCode.BadRequest);
                }
                const patientConsultation = await req.json();
                var data = null;
                if (HttpMethod.post) {
                    data = patientHandler.CreatePatientConsultations(patientId, patientConsultation.decode.oid);
                } else {
                    data = patientHandler.UpdatePatientConsultations(patientConsultation, decode.oid);
                }
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
