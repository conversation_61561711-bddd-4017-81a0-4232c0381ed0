const prescriptionRepository = require('../repositories/prescription-repository')
const logging = require('../common/logging')
const PrescriptionModel = require('../models/prescription-model')

class PrescriptionService {
  async getPrescriptionsByPatient(
    patientId,
    dateFilter = null,
    customDateRange = null,
    searchText = null,
  ) {
    try {
      const result = await prescriptionRepository.getPrescriptionsByPatient(
        patientId,
        dateFilter,
        customDateRange,
        searchText,
      )
      return result
    } catch (error) {
      logging.logError(
        `Failed to fetch prescriptions for patient ${patientId}`,
        error,
      )
      return []
    }
  }

  async getPrescriptionById(prescriptionId) {
    try {
      const result = await prescriptionRepository.getPrescriptionById(
        prescriptionId,
      )
      return result
    } catch (error) {
      logging.logError(
        `Failed to fetch prescription ${prescriptionId} for patient ${patientId}`,
        error,
      )
      return []
    }
  }

  async createOrUpdatePrescriptions(
    patientId,
    doctor,
    prescriptionsData,
    prescriptionId,
    doctorEmail,
  ) {
    try {
      const existingPrescriptions =
        await prescriptionRepository.getPrescriptionsByPatient(patientId)

      const result = []

      let prescriptionToUpdate = existingPrescriptions.find(
        (p) => p.id === prescriptionId,
      )

      if (prescriptionToUpdate) {
        prescriptionToUpdate.medicines = prescriptionsData
        prescriptionToUpdate.doctor = doctor
        prescriptionToUpdate.doctorEmail = doctorEmail 

        const savedPrescription =
          await prescriptionRepository.updatePrescription(
            prescriptionId,
            patientId,
            prescriptionToUpdate,
          )
        result.push({ success: true, data: savedPrescription })
      } else {
        const newPrescription = new PrescriptionModel({
          medicines: prescriptionsData,
          doctor,
          patientId,
          doctorEmail,
        })

        const savedPrescription =
          await prescriptionRepository.createPrescription(
            patientId,
            newPrescription,
          )
        result.push({ success: true, data: savedPrescription })
      }

      return result
    } catch (error) {
      logging.logError(
        `Failed to create or update prescriptions for patient ${patientId}`,
        error,
      )
      throw error
    }
  }
  async searchPrescriptions(
    searchText,
    pageSize,
    continuationToken,
    patientId,
  ) {
    return await prescriptionRepository.searchPrescription(
      searchText,
      pageSize,
      continuationToken,
      patientId,
    )
  }
}

module.exports = new PrescriptionService()
